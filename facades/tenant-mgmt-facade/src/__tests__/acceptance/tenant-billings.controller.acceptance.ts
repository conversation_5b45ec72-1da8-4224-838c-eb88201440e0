import {Client, expect, sinon} from '@loopback/testlab';
import {getToken, setupApplication} from './test-helper';
import {PermissionKey, StatusDto} from '@local/core';
import {TenantMgmtFacadeApplication} from '../../application';
import {SubscriptionProxyService} from '../../services/proxies';
import {TenantBillingsView} from '../../models';

describe('TenantBillingsController', () => {
  let app: TenantMgmtFacadeApplication;
  let client: Client;
  let subscriptionServiceProxyStub: sinon.SinonStubbedInstance<SubscriptionProxyService>;

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
  });

  after(async () => {
    await app.stop();
  });

  beforeEach(() => {
    subscriptionServiceProxyStub = {
      getTenantBillings: sinon.stub(),
      getTenantBillingsCount: sinon.stub(),
      getAllBillingStatus: sinon.stub(),
      // unused stubs for full binding
      findPlanById: sinon.stub(),
      getPlansCount: sinon.stub(),
      getPlans: sinon.stub(),
      createPlan: sinon.stub(),
      getAllPlanStatus: sinon.stub(),
      createCustomer: sinon.stub(),
      getConfigureDevices: sinon.stub(),
      getStorageSize: sinon.stub(),
      handleWebhook: sinon.stub(),
      createPrice: sinon.stub(),
      getSubscriptionTenure: sinon.stub(),
      getPlanHistory: sinon.stub(),
      getCurrencies: sinon.stub(),
      createSubscriptions: sinon.stub(),
      updatePlanById: sinon.stub(),
      createPlanHistory: sinon.stub(),
      getInvoices: sinon.stub(),
      getInvoicesCount: sinon.stub(),
    };
    app
      .bind('services.SubscriptionProxyService')
      .to(subscriptionServiceProxyStub);
  });

  afterEach(() => sinon.restore());

  it('retrieves tenant billings when authorized', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    const mockBillings: TenantBillingsView[] = [
      new TenantBillingsView({id: 'tb1', tenantId: 't1', amount: 100}),
    ];
    subscriptionServiceProxyStub.getTenantBillings.resolves(mockBillings);

    const res = await client
      .get('/tenant-billings')
      .set('Authorization', token)
      .expect(200);

    expect(res.body).to.deepEqual([mockBillings[0].toJSON()]);
  });

  it('retrieves tenant billings count', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    subscriptionServiceProxyStub.getTenantBillingsCount.resolves({count: 2});

    const res = await client
      .get('/tenant-billings/count')
      .set('Authorization', token)
      .expect(200);

    expect(res.body).to.deepEqual({count: 2});
  });

  it('retrieves all billing statuses', async () => {
    const token = getToken([PermissionKey.ViewAllStatuses]);
    const mockStatuses = new StatusDto({
      statuses: {0: 'ACTIVE', 1: 'INACTIVE'},
    });
    subscriptionServiceProxyStub.getAllBillingStatus.resolves(mockStatuses);

    const res = await client
      .get('/tenant-billings/all-status')
      .set('Authorization', token)
      .expect(200);

    expect(res.body).to.deepEqual({statuses: {0: 'ACTIVE', 1: 'INACTIVE'}});
  });

  it('fails with 401 when no token provided', async () => {
    await client.get('/tenant-billings').expect(401);
    sinon.assert.notCalled(subscriptionServiceProxyStub.getTenantBillings);
  });
});
