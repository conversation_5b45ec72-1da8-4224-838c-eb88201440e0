import {Address} from '@sourceloop/ctrl-plane-tenant-management-service';

import {DataObject} from '@loopback/repository';
import {Contact, PlanHistory, Tenant} from '../../models';
import {TenantOnboardDTO} from '../../models/dto/tenant-onboard.dto.model';
import {PlanStatus, PlanTierType, StorageSource} from '@local/core';
import {IMetaData, IPlan} from '../../types';

export const testAddress = 'test-address';
export const testState = 'test-state';

export const mockAddress: DataObject<Address> = {
  address: testAddress,
  city: 'test-city',
  state: testState,
  country: 'test-country',
  zip: 'test-zip',
};

export const testDomain = 'contact.com';
export const mockSubscriptionId = 'test-subscription-id';

export const mockContact: DataObject<Contact> = {
  firstName: 'testcontact',
  lastName: 'dev',
  email: '<EMAIL>',
  phoneNumber: '1234567890',
  countryCode: '+91',
  designation: 'dev',
  isPrimary: false,
};

export const mockTenantOnboardDTO: DataObject<TenantOnboardDTO> = {
  contact: mockContact,
  name: 'testname',
  country: 'India',
  address: testAddress,
  city: 'test-city',
  state: testState,
  zip: 'test-zip',
  key: 'testkey',
  domains: [testDomain],
  lang: 'English',
  files: [
    {
      fileKey: '12121212',
      originalName: 'dummy.pdf',
      source: StorageSource.S3,
    },
  ],
};

export const tenantPayload = {
  contact: {
    firstName: 'dummy',
    lastName: 'tenant',
    isPrimary: true,
    email: '<EMAIL>',
    designation: 'Employee',
    phoneNumber: '9876543219',
    countryCode: '+91',
  },
  key: 'keyTenant',
  overallPlan: {
    planId: 'plan-id',
    tagId: 'tag-id',
  },
  name: 'Tenant',
  lang: 'English',
};
export function buildTenantDto() {
  return new Tenant({
    deleted: false,
    id: 'fab77c2c-19b5-29dc-e565-3b5027395771',
    name: 'Tenant',
    status: 1,
    key: 'keyTenant',
    domains: ['sourcefuse.com'],
    lang: 'English',
    contacts: [
      new Contact({
        id: 'fe3ff526-548c-699c-ff23-fd01f469a36e',
        firstName: 'Kannan',
        lastName: 't',
        email: '<EMAIL>',
        isPrimary: true,
        tenantId: 'fab77c2c-19b5-29dc-e565-3b5027395771',
      }),
    ],
  });
}

export const dummyPlan: IPlan = {
  id: 'plan_12345',
  name: 'Premium Plan',
  description: 'This is a premium subscription plan with advanced features.',
  price: 49.99,
  currencyId: 'USD',
  metaData: {
    version: '1.0',
    category: 'subscription',
    pipelineName: 'p1',
  } as IMetaData,
  tier: PlanTierType.PREMIUM,
  billingCycleId: 'cycle_67890',
  status: PlanStatus.ACTIVE,
  version: '',
  allowedUnlimitedUsers: false,
  costPerUser: 0,
  configureDeviceId: '',
  planSizeId: '',
  planHistories: [] as PlanHistory[],
};

export const mockTenant: DataObject<Tenant> = {
  name: 'test-name',
  status: 1,
  spocUserId: 'test-user-id',
  key: 'test',
  domains: [testDomain],
  lang: 'EN',
};

export const BufferFile = Buffer.from(
  '%PDF-1.4\n' +
    '1 0 obj\n' +
    '<< /Type /Catalog /Pages 2 0 R >>\n' +
    'endobj\n' +
    '2 0 obj\n' +
    '<< /Type /Pages /Kids [3 0 R] /Count 1 >>\n' +
    'endobj\n' +
    '3 0 obj\n' +
    '<< /Type /Page /Parent 2 0 R /MediaBox [0 0 612 792] /Contents 4 0 R >>\n' +
    'endobj\n' +
    '4 0 obj\n' +
    '<< /Length 44 >>\n' +
    'stream\n' +
    'BT /F1 24 Tf 100 700 Td (Hello World) Tj ET\n' +
    'endstream\n' +
    'endobj\n' +
    'xref\n' +
    '0 5\n' +
    '0000000000 65535 f\n' +
    '0000000010 00000 n\n' +
    '0000000083 00000 n\n' +
    '0000000146 00000 n\n' +
    '0000000265 00000 n\n' +
    'trailer\n' +
    '<< /Size 5 /Root 1 0 R >>\n' +
    'startxref\n' +
    '345\n' +
    '%%EOF\n',
  'utf-8',
);
