import {Client, expect, sinon} from '@loopback/testlab';
import {getToken, setupApplication} from './test-helper';
import {PermissionKey} from '@local/core';
import {TenantMgmtFacadeApplication} from '../../application';
import {SubscriptionProxyService} from '../../services/proxies';
import {PlanHistory} from '../../models';

describe('PlanHistoryController', () => {
  let app: TenantMgmtFacadeApplication;
  let client: Client;
  let subscriptionServiceProxyServiceStub: sinon.SinonStubbedInstance<SubscriptionProxyService>;

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
  });

  after(async () => {
    await app.stop();
  });

  beforeEach(() => {
    subscriptionServiceProxyServiceStub = {
      findPlanById: sinon.stub(),
      getConfigureDevices: sinon.stub(),
      getTenantBillings: sinon.stub(),
      getTenantBillingsCount: sinon.stub(),
      getAllBillingStatus: sinon.stub(),
      getStorageSize: sinon.stub(),
      createCustomer: sinon.stub(),
      getSubscriptionTenure: sinon.stub(),
      createPrice: sinon.stub(),
      handleWebhook: sinon.stub(),
      getPlans: sinon.stub(),
      createPlan: sinon.stub(),
      getCurrencies: sinon.stub(),
      getPlansCount: sinon.stub(),
      getPlanHistory: sinon.stub(),
      getAllPlanStatus: sinon.stub(),
      createSubscriptions: sinon.stub(),
      updatePlanById: sinon.stub(), // Added stub for updatePlanById
      createPlanHistory: sinon.stub(), // Added stub for createPlanHistory
      getInvoices: sinon.stub(),
      getInvoicesCount: sinon.stub(),
    };
    app
      .bind('services.SubscriptionProxyService')
      .to(subscriptionServiceProxyServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  it('returns list of plan history records when authorized', async () => {
    const token = getToken([PermissionKey.ViewPlanHistory]);

    const mockHistory: PlanHistory[] = [
      new PlanHistory({
        id: 'history-1',
        planId: 'plan-1',
        price: 100,
        version: 'v2',
      }),
      new PlanHistory({
        id: 'history-2',
        planId: 'plan-2',
        price: 200,
        version: 'v1',
      }),
    ];

    subscriptionServiceProxyServiceStub.getPlanHistory.resolves(mockHistory);

    const res = await client
      .get('/plan-history')
      .set('Authorization', token)
      .expect(200);

    expect(res.body).to.deepEqual(
      mockHistory.map(h => {
        const maybeWithToJSON = h as PlanHistory & {toJSON?: () => object};
        return typeof maybeWithToJSON.toJSON === 'function'
          ? maybeWithToJSON.toJSON()
          : {...h};
      }),
    );
  });

  it('returns empty array when no plan history found', async () => {
    subscriptionServiceProxyServiceStub.getPlanHistory.resolves([]);

    const token = getToken([PermissionKey.ViewPlanHistory]);

    const res = await client
      .get('/plan-history')
      .set('Authorization', token)
      .expect(200);

    expect(res.body).to.deepEqual([]);
  });

  it('fails with 401 when no token is provided', async () => {
    await client.get('/plan-history').expect(401);
    sinon.assert.notCalled(subscriptionServiceProxyServiceStub.getPlanHistory);
  });
});
