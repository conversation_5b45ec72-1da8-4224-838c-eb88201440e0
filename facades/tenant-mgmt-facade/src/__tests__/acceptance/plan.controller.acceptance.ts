import {Client, expect, sinon} from '@loopback/testlab';
import {getToken, setupApplication} from './test-helper';
import {PermissionKey, PlanStatus, PlanTierType, StatusDto} from '@local/core';
import {TenantMgmtFacadeApplication} from '../../application';
import {SubscriptionProxyService} from '../../services/proxies';
import {Plan, PlanHistory} from '../../models';
import {IPlan} from '../../types';
import * as Utils from '../../utils'; // add this near the top with other imports

describe('PlansController', () => {
  let app: TenantMgmtFacadeApplication;
  let client: Client;
  let subscriptionServiceProxyStub: sinon.SinonStubbedInstance<SubscriptionProxyService>;

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
  });

  after(async () => {
    await app.stop();
  });

  beforeEach(() => {
    subscriptionServiceProxyStub = {
      findPlanById: sinon.stub(),
      getPlansCount: sinon.stub(),
      getPlans: sinon.stub(),
      createPlan: sinon.stub(),
      getAllPlanStatus: sinon.stub(),
      createCustomer: sinon.stub(),
      getTenantBillings: sinon.stub(),
      getTenantBillingsCount: sinon.stub(),
      getAllBillingStatus: sinon.stub(),
      // unused stubs but needed for full binding
      getConfigureDevices: sinon.stub(),
      getStorageSize: sinon.stub(),
      handleWebhook: sinon.stub(),
      createPrice: sinon.stub(),
      getSubscriptionTenure: sinon.stub(),
      getPlanHistory: sinon.stub(),
      getCurrencies: sinon.stub(),
      createSubscriptions: sinon.stub(),
      updatePlanById: sinon.stub(), // Added stub for updatePlanById
      createPlanHistory: sinon.stub(), // Added stub for createPlanHistory
      getInvoices: sinon.stub(),
      getInvoicesCount: sinon.stub(),
    };
    app
      .bind('services.SubscriptionProxyService')
      .to(subscriptionServiceProxyStub);
  });

  afterEach(() => sinon.restore());

  it('retrieves a plan by ID when authorized', async () => {
    const token = getToken([PermissionKey.ViewPlan]);
    const mockPlan = new Plan({
      id: 'plan-123',
      name: 'Pro Plan',
      tier: PlanTierType.PREMIUM,
      price: 50,
      status: PlanStatus.ACTIVE,
      version: 'v1.0.0',
      allowedUnlimitedUsers: false,
      costPerUser: 10,
      billingCycleId: 'cycle-1',
      currencyId: 'curr-1',
      configureDeviceId: 'config-1',
      planSizeId: 'size-1',
    });

    subscriptionServiceProxyStub.findPlanById.resolves(mockPlan);

    const res = await client
      .get('/plans/plan-123')
      .set('Authorization', token)
      .expect(200);

    expect(res.body).to.deepEqual(mockPlan.toJSON());
  });

  it('retrieves plan count', async () => {
    const token = getToken([PermissionKey.ViewPlan]);
    const where = {status: PlanStatus.ACTIVE};

    subscriptionServiceProxyStub.getPlansCount.resolves({count: 5});

    const res = await client
      .get('/plans/count')
      .query({where})
      .set('Authorization', token)
      .expect(200);

    expect(res.body).to.deepEqual({count: 5});
  });

  it('retrieves plans list', async () => {
    const token = getToken([PermissionKey.ViewPlan]);
    const mockPlans: IPlan[] = [
      {
        id: 'p1',
        name: 'Basic Plan',
        tier: PlanTierType.STANDARD,
        price: 20,
        status: PlanStatus.ACTIVE,
        version: 'v1',
        allowedUnlimitedUsers: true,
        costPerUser: 0,
        billingCycleId: 'cycle-1',
        currencyId: 'curr-1',
        configureDeviceId: 'config-1',
        planSizeId: 'size-1',
      },
    ];

    subscriptionServiceProxyStub.getPlans.resolves(mockPlans);

    await client.get('/plans').set('Authorization', token).expect(200);
  });

  it('creates a plan successfully', async () => {
    process.env.DEFAULT_PLAN_SIZE_ID = 'size-123';
    process.env.PLAN_VERSION_PATTERN = 'v1.0.0';

    const token = getToken([PermissionKey.CreatePlan]);
    const requestBody = {
      name: 'Enterprise Plan',
      tier: PlanTierType.PREMIUM,
      price: 100,
      allowedUnlimitedUsers: false,
      costPerUser: 5,
      billingCycleId: 'cycle-1',
      currencyId: 'curr-1',
      configureDeviceId: 'config-1',
    };

    const createdPlan = {
      ...requestBody,
      id: 'new-plan',
      version: 'v1.0.0',
    };

    subscriptionServiceProxyStub.createPlan.resolves(createdPlan);

    const res = await client
      .post('/plans')
      .set('Authorization', token)
      .send(requestBody)
      .expect(200);

    expect(res.body).to.containEql(createdPlan);
  });

  it('throws 400 when allowedUnlimitedUsers is false but costPerUser is missing', async () => {
    const token = getToken([PermissionKey.CreatePlan]);
    const invalidBody = {
      name: 'Bad Plan',
      tier: PlanTierType.PREMIUM,
      price: 10,
      allowedUnlimitedUsers: false,
      billingCycleId: 'cycle-1',
      currencyId: 'curr-1',
      configureDeviceId: 'config-1',
    };

    await client
      .post('/plans')
      .set('Authorization', token)
      .send(invalidBody)
      .expect(400);
  });

  it('retrieves all plan statuses', async () => {
    const token = getToken([PermissionKey.ViewAllStatuses]);
    const mockStatuses: StatusDto = new StatusDto({
      statuses: {
        0: 'ACTIVE',
        1: 'INACTIVE',
      },
    });

    subscriptionServiceProxyStub.getAllPlanStatus.resolves(mockStatuses);

    const res = await client
      .get('/plans/all-status')
      .set('Authorization', token)
      .expect(200);
    expect(res.body).to.deepEqual({statuses: {0: 'ACTIVE', 1: 'INACTIVE'}});
  });

  it('fails with 401 when no token provided', async () => {
    await client.get('/plans').expect(401);
    sinon.assert.notCalled(subscriptionServiceProxyStub.getPlans);
  });

  it('updates a plan by id (allowedUnlimitedUsers=true forces costPerUser=0)', async () => {
    process.env.PLAN_VERSION_PATTERN = 'vX.Y.Z';

    const token = getToken([PermissionKey.UpdatePlan]);

    const existingPlan = new Plan({
      id: 'plan-123',
      name: 'Pro Plan',
      tier: PlanTierType.PREMIUM,
      price: 50,
      status: 0,
      version: 'v1.0.0',
      allowedUnlimitedUsers: false,
      costPerUser: 10,
      billingCycleId: 'cycle-1',
      currencyId: 'curr-1',
      configureDeviceId: 'config-1',
      planSizeId: 'size-1',
    });

    // history length = 1 -> count = 3 -> version should be whatever utils returns
    const history = [
      {id: 'h1', planId: 'plan-123', version: 'v0.9.0'},
    ] as unknown as PlanHistory[];

    subscriptionServiceProxyStub.findPlanById.resolves(existingPlan);
    subscriptionServiceProxyStub.getPlanHistory.resolves(history);

    const versionStub = sinon
      .stub(Utils, 'generateInitialVersion')
      .returns('v1.0.2');

    await client
      .patch('/plans/plan-123')
      .set('Authorization', token)
      .send({
        price: 150,
        allowedUnlimitedUsers: true, // triggers costPerUser -> 0
      })
      .expect(204);

    // previous plan snapshot saved
    sinon.assert.calledOnce(subscriptionServiceProxyStub.createPlanHistory);
    sinon.assert.calledWithMatch(
      subscriptionServiceProxyStub.createPlanHistory,
      sinon.match.string, // auth token
      sinon.match({
        planId: 'plan-123',
        version: 'v1.0.0',
        price: 50,
        allowedUnlimitedUsers: false,
        costPerUser: 10,
      }),
    );

    // update applied with forced costPerUser=0 and computed version
    sinon.assert.calledOnce(subscriptionServiceProxyStub.updatePlanById);
    sinon.assert.calledWithMatch(
      subscriptionServiceProxyStub.updatePlanById,
      sinon.match.string, // auth token
      'plan-123',
      {
        price: 150,
        allowedUnlimitedUsers: true,
        costPerUser: 0,
        version: 'v1.0.2',
      },
    );

    versionStub.restore();
    delete process.env.PLAN_VERSION_PATTERN;
  });

  it('returns 400 when updating plan with allowedUnlimitedUsers=false and missing costPerUser', async () => {
    const token = getToken([PermissionKey.UpdatePlan]);

    await client
      .patch('/plans/plan-123')
      .set('Authorization', token)
      .send({
        allowedUnlimitedUsers: false,
        // costPerUser omitted -> 400
      })
      .expect(400);

    sinon.assert.notCalled(subscriptionServiceProxyStub.updatePlanById);
    sinon.assert.notCalled(subscriptionServiceProxyStub.createPlanHistory);
  });

  it('returns 500 when updating plan if PLAN_VERSION_PATTERN is not set', async () => {
    const token = getToken([PermissionKey.UpdatePlan]);

    // Must still resolve these before it hits the env check
    subscriptionServiceProxyStub.findPlanById.resolves(
      new Plan({
        id: 'plan-123',
        name: 'Pro Plan',
        tier: PlanTierType.PREMIUM,
        price: 50,
        status: 0,
        version: 'v1.0.0',
        allowedUnlimitedUsers: false,
        costPerUser: 10,
        billingCycleId: 'cycle-1',
        currencyId: 'curr-1',
        configureDeviceId: 'config-1',
        planSizeId: 'size-1',
      }),
    );
    subscriptionServiceProxyStub.getPlanHistory.resolves([]);

    // Ensure not set
    delete process.env.PLAN_VERSION_PATTERN;

    await client
      .patch('/plans/plan-123')
      .set('Authorization', token)
      .send({
        price: 60,
        allowedUnlimitedUsers: false,
        costPerUser: 12,
      })
      .expect(500);

    // No writes should happen
    sinon.assert.notCalled(subscriptionServiceProxyStub.createPlanHistory);
    sinon.assert.notCalled(subscriptionServiceProxyStub.updatePlanById);
  });

  it('updates plan status by id', async () => {
    const token = getToken([PermissionKey.UpdatePlan]);

    await client
      .patch('/plans/plan-123/status')
      .set('Authorization', token)
      .send({status: 1}) // e.g., PlanStatus.INACTIVE
      .expect(204);

    sinon.assert.calledOnce(subscriptionServiceProxyStub.updatePlanById);
    sinon.assert.calledWithMatch(
      subscriptionServiceProxyStub.updatePlanById,
      sinon.match.string, // auth
      'plan-123',
      {status: 1},
    );
  });

  it('returns 500 when creating a plan if DEFAULT_PLAN_SIZE_ID is not set', async () => {
    process.env.PLAN_VERSION_PATTERN = 'v1.0.0';
    delete process.env.DEFAULT_PLAN_SIZE_ID;

    const token = getToken([PermissionKey.CreatePlan]);
    const requestBody = {
      name: 'Enterprise Plan',
      tier: PlanTierType.PREMIUM,
      price: 100,
      allowedUnlimitedUsers: false,
      costPerUser: 5,
      billingCycleId: 'cycle-1',
      currencyId: 'curr-1',
      configureDeviceId: 'config-1',
    };

    await client
      .post('/plans')
      .set('Authorization', token)
      .send(requestBody)
      .expect(500);

    delete process.env.PLAN_VERSION_PATTERN;
  });

  it('returns 500 when creating a plan if PLAN_VERSION_PATTERN is not set', async () => {
    process.env.DEFAULT_PLAN_SIZE_ID = 'size-123';
    delete process.env.PLAN_VERSION_PATTERN;

    const token = getToken([PermissionKey.CreatePlan]);
    const requestBody = {
      name: 'Enterprise Plan',
      tier: PlanTierType.PREMIUM,
      price: 100,
      allowedUnlimitedUsers: false,
      costPerUser: 5,
      billingCycleId: 'cycle-1',
      currencyId: 'curr-1',
      configureDeviceId: 'config-1',
    };

    await client
      .post('/plans')
      .set('Authorization', token)
      .send(requestBody)
      .expect(500);

    delete process.env.DEFAULT_PLAN_SIZE_ID;
  });

  it('updates a plan by id with allowedUnlimitedUsers=false and valid costPerUser', async () => {
    process.env.PLAN_VERSION_PATTERN = 'vX.Y.Z';
    const token = getToken([PermissionKey.UpdatePlan]);

    const existingPlan = new Plan({
      id: 'plan-abc',
      name: 'Standard Plan',
      tier: PlanTierType.STANDARD,
      price: 25,
      status: 0,
      version: 'v1.0.1',
      allowedUnlimitedUsers: true,
      costPerUser: 0,
      billingCycleId: 'cycle-1',
      currencyId: 'curr-1',
      configureDeviceId: 'config-1',
      planSizeId: 'size-1',
    });

    subscriptionServiceProxyStub.findPlanById.resolves(existingPlan);
    subscriptionServiceProxyStub.getPlanHistory.resolves([
      {id: 'h1'},
    ] as unknown as PlanHistory[]);

    const versionStub = sinon
      .stub(Utils, 'generateInitialVersion')
      .returns('v1.0.3');

    await client
      .patch('/plans/plan-abc')
      .set('Authorization', token)
      .send({
        price: 30,
        allowedUnlimitedUsers: false,
        costPerUser: 3,
      })
      .expect(204);

    sinon.assert.calledOnce(subscriptionServiceProxyStub.createPlanHistory);
    sinon.assert.calledWithMatch(
      subscriptionServiceProxyStub.updatePlanById,
      sinon.match.string,
      'plan-abc',
      {
        price: 30,
        allowedUnlimitedUsers: false,
        costPerUser: 3,
        version: 'v1.0.3',
      },
    );

    versionStub.restore();
    delete process.env.PLAN_VERSION_PATTERN;
  });
});
