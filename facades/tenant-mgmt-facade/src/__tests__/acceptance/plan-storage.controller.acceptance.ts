import {Client, expect, sinon} from '@loopback/testlab';
import {getToken, setupApplication} from './test-helper';
import {PermissionKey} from '@local/core';
import {TenantMgmtFacadeApplication} from '../../application';
import {SubscriptionProxyService} from '../../services/proxies';
import {PlanSizes} from '../../models';
import {IPlanSizes} from '../../types';

describe('PlanStorageController', () => {
  let app: TenantMgmtFacadeApplication;
  let client: Client;
  let subscriptionServiceProxyServiceStub: sinon.SinonStubbedInstance<SubscriptionProxyService>;

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
  });

  after(async () => {
    await app.stop();
  });

  beforeEach(() => {
    subscriptionServiceProxyServiceStub = {
      findPlanById: sinon.stub(),
      getConfigureDevices: sinon.stub(),
      createPrice: sinon.stub(),
      getStorageSize: sinon.stub(),
      getSubscriptionTenure: sinon.stub(),
      getPlans: sinon.stub(),
      createPlan: sinon.stub(),
      getCurrencies: sinon.stub(),
      createCustomer: sinon.stub(),
      getTenantBillings: sinon.stub(),
      getTenantBillingsCount: sinon.stub(),
      getAllBillingStatus: sinon.stub(),
      getPlansCount: sinon.stub(),
      handleWebhook: sinon.stub(),
      getPlanHistory: sinon.stub(),
      getAllPlanStatus: sinon.stub(),
      createSubscriptions: sinon.stub(),
      updatePlanById: sinon.stub(), // Added stub for updatePlanById
      createPlanHistory: sinon.stub(), // Added stub for createPlanHistory
      getInvoices: sinon.stub(),
      getInvoicesCount: sinon.stub(),
    };
    app
      .bind('services.SubscriptionProxyService')
      .to(subscriptionServiceProxyServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  it('returns list of plan sizes when authorized', async () => {
    const token = getToken([PermissionKey.ViewPlanSizes]);

    const mockPlanSizes: IPlanSizes[] = [
      new PlanSizes({
        id: 'size-1',
        size: '50GB',
        config: {ssd: true},
      }),
      new PlanSizes({
        id: 'size-2',
        size: '100GB',
        config: {ssd: false},
      }),
    ];

    subscriptionServiceProxyServiceStub.getStorageSize.resolves(mockPlanSizes);

    const res = await client
      .get('/plan-storage')
      .set('Authorization', token)
      .expect(200);

    expect(res.body).to.deepEqual(
      mockPlanSizes.map(s => {
        // Cast to type with optional toJSON method
        const maybeWithToJSON = s as IPlanSizes & {toJSON?: () => object};
        return typeof maybeWithToJSON.toJSON === 'function'
          ? maybeWithToJSON.toJSON()
          : {...s};
      }),
    );
  });

  it('returns empty array when no plan sizes found', async () => {
    subscriptionServiceProxyServiceStub.getStorageSize.resolves([]);

    const token = getToken([PermissionKey.ViewPlanSizes]);

    const res = await client
      .get('/plan-storage')
      .set('Authorization', token)
      .expect(200);

    expect(res.body).to.deepEqual([]);
  });

  it('fails with 401 when no token is provided', async () => {
    await client.get('/plan-storage').expect(401);
    sinon.assert.notCalled(subscriptionServiceProxyServiceStub.getStorageSize);
  });
});
