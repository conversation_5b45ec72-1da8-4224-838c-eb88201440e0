import {Client, expect, sinon} from '@loopback/testlab';
import {getToken, setupApplication} from './test-helper';
import {PermissionKey} from '@local/core';
import {TenantMgmtFacadeApplication} from '../../application';
import {SubscriptionProxyService} from '../../services/proxies';
import {ConfigureDevice} from '../../models';

describe('ConfigureDeviceController', () => {
  let app: TenantMgmtFacadeApplication;
  let client: Client;
  let subscriptionServiceProxyServiceStub: sinon.SinonStubbedInstance<SubscriptionProxyService>;

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
  });

  after(async () => {
    await app.stop();
  });

  beforeEach(() => {
    subscriptionServiceProxyServiceStub = {
      findPlanById: sinon.stub(),
      getConfigureDevices: sinon.stub(),
      getStorageSize: sinon.stub(),
      getSubscriptionTenure: sinon.stub(),
      getPlans: sinon.stub(),
      createPlan: sinon.stub(),
      getCurrencies: sinon.stub(),
      getPlansCount: sinon.stub(),
      getPlanHistory: sinon.stub(),
      getAllPlanStatus: sinon.stub(),
      createSubscriptions: sinon.stub(),
      updatePlanById: sinon.stub(), // Added stub for updatePlanById
      createPlanHistory: sinon.stub(), // Added stub for createPlanHistory
      createPrice: sinon.stub(), // Added stub for createPrice
      createCustomer: sinon.stub(),
      getTenantBillings: sinon.stub(),
      getTenantBillingsCount: sinon.stub(),
      getAllBillingStatus: sinon.stub(),
      handleWebhook: sinon.stub(),
      getInvoices: sinon.stub(),
      getInvoicesCount: sinon.stub(),
    };
    app
      .bind('services.SubscriptionProxyService')
      .to(subscriptionServiceProxyServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  it('returns list of configure devices when authorized', async () => {
    const token = getToken([PermissionKey.ViewConfigureDevices]);

    const mockDevices: ConfigureDevice[] = [
      new ConfigureDevice({min: 1, max: 5}),
    ];
    subscriptionServiceProxyServiceStub.getConfigureDevices.resolves(
      mockDevices,
    );

    const res = await client
      .get('/configure-devices')
      .set('Authorization', token)
      .expect(200);
    expect(res.body).to.deepEqual(
      mockDevices.map(d => (d.toJSON ? d.toJSON() : {...d})),
    );
  });

  it('returns empty array when no devices found', async () => {
    subscriptionServiceProxyServiceStub.getConfigureDevices.resolves([]);

    const token = getToken([PermissionKey.ViewConfigureDevices]);

    const res = await client
      .get('/configure-devices')
      .set('Authorization', token)
      .expect(200);

    expect(res.body).to.deepEqual([]);
  });

  it('fails with 401 when no token is provided', async () => {
    await client.get('/configure-devices').expect(401);
    sinon.assert.notCalled(
      subscriptionServiceProxyServiceStub.getConfigureDevices,
    );
  });
});
