// Copyright (c) 2025
import {Client, expect} from '@loopback/testlab';
import {getToken} from './test-helper';
import sinon from 'sinon';
import {TenantMgmtFacadeApplication} from '../../application';
import {SubscriptionProxyService} from '../../services/proxies';
import {HTTP_STATUS, InvoiceStatus, PermissionKey} from '@local/core';
import {Invoice} from '../../models';

const basePath = '/billing-invoice';

let subscriptionServiceProxyServiceStub: sinon.SinonStubbedInstance<SubscriptionProxyService>;

describe('BillingInvoiceController', () => {
  let app: TenantMgmtFacadeApplication;
  let client: Client;

  before('setupApplication', async () => {
    subscriptionServiceProxyServiceStub = {
      getInvoices: sinon.stub(),
      getInvoicesCount: sinon.stub(),
    };
    ({app, client} = await require('./test-helper').setupApplication());
  });

  beforeEach(async () => {
    subscriptionServiceProxyServiceStub.getInvoices.reset();
    subscriptionServiceProxyServiceStub.getInvoicesCount.reset();

    app
      .bind('services.SubscriptionProxyService')
      .to(subscriptionServiceProxyServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  after(async () => {
    await app.stop();
  });

  it('invokes GET /billing-invoice with valid token', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    const now = new Date();
    const invoicesMock = [
      {
        id: 'inv-1',
        invoiceId: 'INV-001',
        billingCustomerId: 'cust-1',
        amount: 1000,
        tax: 100,
        discount: 0,
        dueDate: new Date(
          now.getTime() + 7 * 24 * 60 * 60 * 1000,
        ).toISOString(),
        invoiceStatus: 'paid',
        createdOn: now.toISOString(),
      },
      {
        id: 'inv-2',
        invoiceId: 'INV-002',
        billingCustomerId: 'cust-2',
        amount: 2000,
        tax: 200,
        discount: 50,
        dueDate: new Date(
          now.getTime() + 14 * 24 * 60 * 60 * 1000,
        ).toISOString(),
        invoiceStatus: 'open',
        createdOn: now.toISOString(),
      },
    ];
    subscriptionServiceProxyServiceStub.getInvoices.resolves(
      invoicesMock.map(obj => {
        const inst = new Invoice({
          ...obj,
          invoiceStatus: obj.invoiceStatus as InvoiceStatus,
          createdOn: new Date(obj.createdOn),
        });
        inst.toJSON = function () {
          return {...obj};
        };
        inst.toObject = function () {
          return {...obj};
        };
        return inst;
      }),
    );

    const {body} = await client
      .get(basePath)
      .set('Authorization', `${token}`)
      .expect(HTTP_STATUS.OK);

    expect(body).to.containDeep(invoicesMock);
  });

  it('covers token extraction and service call in GET /billing-invoice', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    const filter = {where: {amount: 1000}};
    const expected = [
      {
        id: 'inv-3',
        invoiceId: 'INV-003',
        billingCustomerId: 'cust-3',
        amount: 1000,
        tax: 100,
        discount: 0,
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        invoiceStatus: 'paid',
        createdOn: new Date().toISOString(),
      },
    ];
    subscriptionServiceProxyServiceStub.getInvoices.resolves(
      expected.map(obj => {
        const inst = new Invoice({
          ...obj,
          invoiceStatus: obj.invoiceStatus as InvoiceStatus,
          createdOn: new Date(obj.createdOn),
        });
        inst.toJSON = function () {
          return {...obj};
        };
        inst.toObject = function () {
          return {...obj};
        };
        return inst;
      }),
    );
    const {body} = await client
      .get(basePath)
      .set('Authorization', `${token}`)
      .query({filter: JSON.stringify(filter)})
      .expect(HTTP_STATUS.OK);

    expect(
      subscriptionServiceProxyServiceStub.getInvoices.calledOnce,
    ).to.be.true();
    expect(
      subscriptionServiceProxyServiceStub.getInvoices.firstCall.args[0],
    ).to.equal(token);
    expect(body).to.containDeep(expected);
  });

  it('covers token extraction and service call in GET /billing-invoice/count', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    const where = {amount: 1000};
    const expected = {count: 5};
    const spy =
      subscriptionServiceProxyServiceStub.getInvoicesCount.resolves(expected);

    const {body} = await client
      .get(`${basePath}/count`)
      .set('Authorization', `${token}`)
      .query({where: JSON.stringify(where)})
      .expect(HTTP_STATUS.OK);

    expect(spy.calledOnce).to.be.true();
    expect(spy.firstCall.args[0]).to.equal(token);
    expect(body).to.eql(expected);
  });
  it('responds with 401 Unauthorized for missing token on GET /billing-invoice', async () => {
    await client.get(basePath).expect(HTTP_STATUS.UNAUTHORIZED);
  });

  it('invokes GET /billing-invoice/count with valid token', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    const countMock = {count: 2};
    subscriptionServiceProxyServiceStub.getInvoicesCount.resolves(countMock);

    const {body} = await client
      .get(`${basePath}/count`)
      .set('Authorization', `${token}`)
      .expect(HTTP_STATUS.OK);

    expect(body).to.eql(countMock);
  });

  it('responds with 401 Unauthorized for missing token on GET /billing-invoice/count', async () => {
    await client.get(`${basePath}/count`).expect(HTTP_STATUS.UNAUTHORIZED);
  });
  it('invokes GET /billing-invoice with filter param', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    const filter = {where: {invoiceStatus: 'paid'}};
    subscriptionServiceProxyServiceStub.getInvoices.resolves([
      (() => {
        const obj = {
          id: 'inv-1',
          invoiceId: 'INV-001',
          billingCustomerId: 'cust-1',
          amount: 1000,
          tax: 100,
          discount: 0,
          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          invoiceStatus: 'paid' as InvoiceStatus,
          createdOn: new Date(),
        };
        const inst = new Invoice(obj);
        inst.toJSON = function () {
          return {...obj, createdOn: obj.createdOn.toISOString()};
        };
        inst.toObject = function () {
          return {...obj, createdOn: obj.createdOn.toISOString()};
        };
        return inst;
      })(),
    ]);
    const {body} = await client
      .get(basePath)
      .set('Authorization', `${token}`)
      .query({filter: JSON.stringify(filter)})
      .expect(HTTP_STATUS.OK);

    expect(body).to.be.an.Array();
    expect(body[0].invoiceStatus).to.eql('paid');
  });

  it('invokes GET /billing-invoice/count with where param', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    const where = {invoiceStatus: 'paid'};
    subscriptionServiceProxyServiceStub.getInvoicesCount.resolves({count: 1});

    const {body} = await client
      .get(`${basePath}/count`)
      .set('Authorization', `${token}`)
      .query({where: JSON.stringify(where)})
      .expect(HTTP_STATUS.OK);

    expect(body.count).to.eql(1);
  });

  it('handles error in GET /billing-invoice', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    subscriptionServiceProxyServiceStub.getInvoices.rejects(
      new Error('Test error'),
    );
    await client.get(basePath).set('Authorization', `${token}`).expect(500);
  });

  it('handles error in GET /billing-invoice/count', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    subscriptionServiceProxyServiceStub.getInvoicesCount.rejects(
      new Error('Test error'),
    );
    await client
      .get(`${basePath}/count`)
      .set('Authorization', `${token}`)
      .expect(500);
  });

  it('handles empty authorization header in GET /billing-invoice', async () => {
    subscriptionServiceProxyServiceStub.getInvoices.resolves([]);

    await client
      .get(basePath)
      .set('Authorization', '') // Empty authorization header
      .expect(HTTP_STATUS.UNAUTHORIZED);
  });

  it('handles empty authorization header in GET /billing-invoice/count', async () => {
    subscriptionServiceProxyServiceStub.getInvoicesCount.resolves({count: 0});

    await client
      .get(`${basePath}/count`)
      .set('Authorization', '') // Empty authorization header
      .expect(HTTP_STATUS.UNAUTHORIZED);
  });

  it('invokes GET /billing-invoice with complex filter', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    const complexFilter = {
      where: {
        and: [
          {amount: {gte: 1000}},
          {invoiceStatus: 'paid'},
          {createdOn: {gte: new Date('2024-01-01').toISOString()}},
        ],
      },
      order: ['createdOn DESC'],
      limit: 10,
      skip: 0,
    };

    const mockInvoices = [
      {
        id: 'inv-complex',
        invoiceId: 'INV-COMPLEX',
        billingCustomerId: 'cust-complex',
        amount: 1500,
        tax: 150,
        discount: 0,
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        invoiceStatus: 'paid',
        createdOn: new Date().toISOString(),
      },
    ];

    subscriptionServiceProxyServiceStub.getInvoices.resolves(
      mockInvoices.map(obj => {
        const inst = new Invoice({
          ...obj,
          invoiceStatus: obj.invoiceStatus as any,
          createdOn: new Date(obj.createdOn),
        });
        inst.toJSON = function () {
          return {...obj};
        };
        inst.toObject = function () {
          return {...obj};
        };
        return inst;
      }),
    );

    const {body} = await client
      .get(basePath)
      .set('Authorization', `${token}`)
      .query({filter: JSON.stringify(complexFilter)})
      .expect(HTTP_STATUS.OK);

    expect(
      subscriptionServiceProxyServiceStub.getInvoices.calledOnce,
    ).to.be.true();
    expect(
      subscriptionServiceProxyServiceStub.getInvoices.firstCall.args[0],
    ).to.equal(token);
    expect(
      subscriptionServiceProxyServiceStub.getInvoices.firstCall.args[1],
    ).to.eql(complexFilter);
    expect(body).to.containDeep(mockInvoices);
  });

  it('invokes GET /billing-invoice/count with complex where clause', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    const complexWhere = {
      and: [
        {amount: {gte: 1000}},
        {invoiceStatus: {inq: ['paid', 'open']}},
        {createdOn: {gte: new Date('2024-01-01').toISOString()}},
      ],
    };

    subscriptionServiceProxyServiceStub.getInvoicesCount.resolves({count: 5});

    const {body} = await client
      .get(`${basePath}/count`)
      .set('Authorization', `${token}`)
      .query({where: JSON.stringify(complexWhere)})
      .expect(HTTP_STATUS.OK);

    expect(
      subscriptionServiceProxyServiceStub.getInvoicesCount.calledOnce,
    ).to.be.true();
    expect(
      subscriptionServiceProxyServiceStub.getInvoicesCount.firstCall.args[0],
    ).to.equal(token);
    expect(
      subscriptionServiceProxyServiceStub.getInvoicesCount.firstCall.args[1],
    ).to.eql(complexWhere);
    expect(body.count).to.eql(5);
  });

  it('handles null/undefined filter in GET /billing-invoice', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    subscriptionServiceProxyServiceStub.getInvoices.resolves([]);

    const {body} = await client
      .get(basePath)
      .set('Authorization', `${token}`)
      .expect(HTTP_STATUS.OK);

    expect(
      subscriptionServiceProxyServiceStub.getInvoices.calledOnce,
    ).to.be.true();
    expect(
      subscriptionServiceProxyServiceStub.getInvoices.firstCall.args[0],
    ).to.equal(token);
    expect(
      subscriptionServiceProxyServiceStub.getInvoices.firstCall.args[1],
    ).to.be.undefined();
    expect(body).to.eql([]);
  });

  it('handles null/undefined where in GET /billing-invoice/count', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    subscriptionServiceProxyServiceStub.getInvoicesCount.resolves({count: 0});

    const {body} = await client
      .get(`${basePath}/count`)
      .set('Authorization', `${token}`)
      .expect(HTTP_STATUS.OK);

    expect(
      subscriptionServiceProxyServiceStub.getInvoicesCount.calledOnce,
    ).to.be.true();
    expect(
      subscriptionServiceProxyServiceStub.getInvoicesCount.firstCall.args[0],
    ).to.equal(token);
    expect(
      subscriptionServiceProxyServiceStub.getInvoicesCount.firstCall.args[1],
    ).to.be.undefined();
    expect(body.count).to.eql(0);
  });

  it('handles undefined authorization header in GET /billing-invoice', async () => {
    subscriptionServiceProxyServiceStub.getInvoices.resolves([]);

    await client
      .get(basePath)
      // Don't set Authorization header at all to test undefined case
      .expect(HTTP_STATUS.UNAUTHORIZED);
  });

  it('handles undefined authorization header in GET /billing-invoice/count', async () => {
    subscriptionServiceProxyServiceStub.getInvoicesCount.resolves({count: 0});

    await client
      .get(`${basePath}/count`)
      // Don't set Authorization header at all to test undefined case
      .expect(HTTP_STATUS.UNAUTHORIZED);
  });
});
