import {expect, sinon} from '@loopback/testlab';
import {
  FileAdapterService,
  checkForEmbeddedJavascriptFromUrl,
} from '../../services/file-adapter.service';
import {HttpErrors} from '@loopback/rest';
import {ILogger} from '@sourceloop/core';
import {GetObjectCommand, HeadObjectCommandOutput} from '@aws-sdk/client-s3';
import {PDFDocument, PDFCatalog} from 'pdf-lib';
import {Readable} from 'stream';
import {FileMetadata} from '../../types';
import {S3WithSigner} from 'loopback4-s3';
import {S3HelperService} from '../../services/s3-helper.service';
import {CONTENT_TYPES} from '@local/core';

const dummySignedUrl = 'https://signed-url';
describe('FileAdapterService (Unit)', () => {
  let s3Stub: sinon.SinonStubbedInstance<S3WithSigner>;
  let signedUrlServiceStub: sinon.SinonStubbedInstance<S3HelperService>;
  let service: FileAdapterService;

  const bucket = 'test-bucket';
  const fileKey = 'test.pdf';

  beforeEach(() => {
    process.env.AWS_S3_BUCKET = bucket;

    s3Stub = {
      headObject: sinon.stub(),
      send: sinon.stub(),
    } as unknown as sinon.SinonStubbedInstance<S3WithSigner>;

    signedUrlServiceStub = {
      getSignedUrl: sinon.stub(),
    } as unknown as sinon.SinonStubbedInstance<S3HelperService>;

    const loggerStub: ILogger = {
      info: sinon.stub(),
      error: sinon.stub(),
      warn: sinon.stub(),
      debug: sinon.stub(),
      log: sinon.stub(), // ✅ required
    };

    service = new FileAdapterService(s3Stub, signedUrlServiceStub, loggerStub);
  });

  afterEach(() => sinon.restore());

  describe('generateFileInfoWithSignedUrl', () => {
    it('should return file info with signed URL', async () => {
      const metadata: HeadObjectCommandOutput = {
        Metadata: {foo: 'bar'},
        ContentType: CONTENT_TYPES.PDF,
        ContentLength: 123,
        LastModified: new Date(),
        $metadata: {},
      };
      s3Stub.headObject.resolves(metadata);
      signedUrlServiceStub.getSignedUrl.resolves(dummySignedUrl);

      const res = await service.generateFileInfoWithSignedUrl(fileKey);
      expect(res.metadata).to.eql(metadata.Metadata);
      expect(res.downloadUrl).to.eql(dummySignedUrl);
    });

    it('should throw BadRequest if fileKey is empty', async () => {
      await expect(
        service.generateFileInfoWithSignedUrl(''),
      ).to.be.rejectedWith(HttpErrors.BadRequest);
    });
  });

  describe('generateSignedUrl', () => {
    it('should return signed URL with default bucket', async () => {
      signedUrlServiceStub.getSignedUrl.resolves(dummySignedUrl);
      const res = await service.generateSignedUrl(fileKey);
      expect(res).to.eql(dummySignedUrl);
    });

    it('should return signed URL with custom bucket', async () => {
      signedUrlServiceStub.getSignedUrl.resolves(dummySignedUrl);
      const res = await service.generateSignedUrl(fileKey, 'custom-bucket');
      expect(res).to.eql(dummySignedUrl);
      sinon.assert.calledWithMatch(
        signedUrlServiceStub.getSignedUrl,
        sinon.match.instanceOf(GetObjectCommand),
      );
    });

    it('should throw BadRequest if no fileKey', async () => {
      await expect(service.generateSignedUrl('')).to.be.rejectedWith(
        HttpErrors.BadRequest,
      );
    });

    it('should throw BadRequest if no bucket is set', async () => {
      delete process.env.AWS_S3_BUCKET;
      await expect(service.generateSignedUrl(fileKey)).to.be.rejectedWith(
        /Bucket is not provided/,
      );
    });
  });

  describe('generateLocalStackViewUrl', () => {
    it('should return signed URL for PDF with inline content disposition', async () => {
      signedUrlServiceStub.getSignedUrl.resolves(dummySignedUrl);
      const res = await service.generateLocalStackViewUrl(
        'test.pdf',
        'document.pdf',
      );
      expect(res).to.eql(dummySignedUrl);

      sinon.assert.calledWithMatch(
        signedUrlServiceStub.getSignedUrl,
        sinon.match((command: GetObjectCommand) => {
          return (
            command.input.ResponseContentType === 'application/pdf' &&
            command.input.ResponseContentDisposition === 'inline'
          );
        }),
      );
    });

    it('should return signed URL for JPEG image with proper content type', async () => {
      signedUrlServiceStub.getSignedUrl.resolves(dummySignedUrl);
      const res = await service.generateLocalStackViewUrl(
        'test.jpg',
        'image.jpg',
      );
      expect(res).to.eql(dummySignedUrl);

      sinon.assert.calledWithMatch(
        signedUrlServiceStub.getSignedUrl,
        sinon.match((command: GetObjectCommand) => {
          return (
            command.input.ResponseContentType === 'image/jpeg' &&
            command.input.ResponseContentDisposition === 'inline'
          );
        }),
      );
    });

    it('should return signed URL for PNG image with proper content type', async () => {
      signedUrlServiceStub.getSignedUrl.resolves(dummySignedUrl);
      const res = await service.generateLocalStackViewUrl(
        'test.png',
        'image.png',
      );
      expect(res).to.eql(dummySignedUrl);

      sinon.assert.calledWithMatch(
        signedUrlServiceStub.getSignedUrl,
        sinon.match((command: GetObjectCommand) => {
          return (
            command.input.ResponseContentType === 'image/png' &&
            command.input.ResponseContentDisposition === 'inline'
          );
        }),
      );
    });

    it('should return signed URL for text file with proper content type', async () => {
      signedUrlServiceStub.getSignedUrl.resolves(dummySignedUrl);
      const res = await service.generateLocalStackViewUrl(
        'test.txt',
        'document.txt',
      );
      expect(res).to.eql(dummySignedUrl);

      sinon.assert.calledWithMatch(
        signedUrlServiceStub.getSignedUrl,
        sinon.match((command: GetObjectCommand) => {
          return (
            command.input.ResponseContentType === 'text/plain' &&
            command.input.ResponseContentDisposition === 'inline'
          );
        }),
      );
    });

    it('should use default content type for unknown file extensions', async () => {
      signedUrlServiceStub.getSignedUrl.resolves(dummySignedUrl);
      const res = await service.generateLocalStackViewUrl(
        'test.unknown',
        'file.unknown',
      );
      expect(res).to.eql(dummySignedUrl);

      sinon.assert.calledWithMatch(
        signedUrlServiceStub.getSignedUrl,
        sinon.match((command: GetObjectCommand) => {
          return (
            command.input.ResponseContentType === 'application/octet-stream' &&
            command.input.ResponseContentDisposition === 'inline'
          );
        }),
      );
    });

    it('should use fileKey for extension detection when originalName not provided', async () => {
      signedUrlServiceStub.getSignedUrl.resolves(dummySignedUrl);
      const res = await service.generateLocalStackViewUrl('document.pdf');
      expect(res).to.eql(dummySignedUrl);

      sinon.assert.calledWithMatch(
        signedUrlServiceStub.getSignedUrl,
        sinon.match((command: GetObjectCommand) => {
          return (
            command.input.ResponseContentType === 'application/pdf' &&
            command.input.ResponseContentDisposition === 'inline'
          );
        }),
      );
    });

    it('should work with custom bucket', async () => {
      signedUrlServiceStub.getSignedUrl.resolves(dummySignedUrl);
      const res = await service.generateLocalStackViewUrl(
        'test.pdf',
        'document.pdf',
        'custom-bucket',
      );
      expect(res).to.eql(dummySignedUrl);

      sinon.assert.calledWithMatch(
        signedUrlServiceStub.getSignedUrl,
        sinon.match((command: GetObjectCommand) => {
          return (
            command.input.Bucket === 'custom-bucket' &&
            command.input.ResponseContentType === 'application/pdf' &&
            command.input.ResponseContentDisposition === 'inline'
          );
        }),
      );
    });

    it('should throw BadRequest if fileKey is empty', async () => {
      await expect(service.generateLocalStackViewUrl('')).to.be.rejectedWith(
        HttpErrors.BadRequest,
      );
    });

    it('should throw BadRequest if no bucket is set', async () => {
      delete process.env.AWS_S3_BUCKET;
      await expect(
        service.generateLocalStackViewUrl('test.pdf'),
      ).to.be.rejectedWith(HttpErrors.BadRequest);
    });

    it('should handle JPEG extension correctly', async () => {
      signedUrlServiceStub.getSignedUrl.resolves(dummySignedUrl);
      const res = await service.generateLocalStackViewUrl(
        'test.jpeg',
        'image.jpeg',
      );
      expect(res).to.eql(dummySignedUrl);

      sinon.assert.calledWithMatch(
        signedUrlServiceStub.getSignedUrl,
        sinon.match((command: GetObjectCommand) => {
          return command.input.ResponseContentType === 'image/jpeg';
        }),
      );
    });

    it('should handle GIF extension correctly', async () => {
      signedUrlServiceStub.getSignedUrl.resolves(dummySignedUrl);
      const res = await service.generateLocalStackViewUrl(
        'test.gif',
        'animation.gif',
      );
      expect(res).to.eql(dummySignedUrl);

      sinon.assert.calledWithMatch(
        signedUrlServiceStub.getSignedUrl,
        sinon.match((command: GetObjectCommand) => {
          return command.input.ResponseContentType === 'image/gif';
        }),
      );
    });

    it('should handle HTML extension correctly', async () => {
      signedUrlServiceStub.getSignedUrl.resolves(dummySignedUrl);
      const res = await service.generateLocalStackViewUrl(
        'test.html',
        'page.html',
      );
      expect(res).to.eql(dummySignedUrl);

      sinon.assert.calledWithMatch(
        signedUrlServiceStub.getSignedUrl,
        sinon.match((command: GetObjectCommand) => {
          return command.input.ResponseContentType === 'text/html';
        }),
      );
    });
  });

  describe('generateFileResponse', () => {
    const validFile: FileMetadata = {
      fieldname: 'file',
      originalname: 'file.pdf',
      encoding: '7bit',
      mimetype: CONTENT_TYPES.PDF,
      size: 1234,
      bucket: 'test-bucket',
      key: 'file.pdf',
      acl: 'private',
      contentType: CONTENT_TYPES.PDF,
      contentDisposition: null,
      contentEncoding: null,
      storageClass: 'STANDARD',
      serverSideEncryption: null,
      metadata: {
        fieldName: 'file',
      },
      location: 'https://test-bucket.s3.amazonaws.com/file.pdf',
      etag: '1234567890abcdef',
      versionId: '1',
    };

    beforeEach(() => {
      sinon
        .stub(
          require('../../services/file-adapter.service'),
          'checkForEmbeddedJavascriptFromUrl',
        )
        .resolves();
    });

    it('should throw BadRequest for disallowed extension', async () => {
      const badFile = {...validFile, originalname: 'file.exe'};
      await expect(service.generateFileResponse(badFile)).to.be.rejectedWith(
        /Disallowed file extension/,
      );
    });

    it('should throw BadRequest for disallowed mimetype', async () => {
      const badFile = {...validFile, mimetype: 'application/x-msdownload'};
      await expect(service.generateFileResponse(badFile)).to.be.rejectedWith(
        /Invalid file type/,
      );
    });
  });
});

describe('checkForEmbeddedJavascriptFromUrl', () => {
  let s3Stub: sinon.SinonStubbedInstance<S3WithSigner>;
  let loggerStub: ILogger;

  beforeEach(() => {
    s3Stub = {
      send: sinon.stub(),
    } as unknown as sinon.SinonStubbedInstance<S3WithSigner>;

    // ✅ Assign directly to the outer variable, do NOT use `const` or `let` here
    loggerStub = {
      error: sinon.stub(),
      info: sinon.stub(),
      warn: sinon.stub(),
      debug: sinon.stub(),
      log: sinon.stub(),
    };
  });

  it('should pass for a safe PDF', async () => {
    const readable = new Readable();
    readable.push(Buffer.from('pdf-binary-data'));
    readable.push(null);
    s3Stub.send.resolves({Body: readable});

    const catalogStub: PDFCatalog = {
      lookup: sinon.stub().returns(null),
      // You can add more methods here if needed in other tests
    } as unknown as PDFCatalog;

    const pdfDocStub: PDFDocument = {
      catalog: catalogStub,
      getPages: sinon.stub().returns([]),
      // add any more required properties/methods here as needed
    } as unknown as PDFDocument;

    sinon.stub(PDFDocument, 'load').resolves(pdfDocStub);

    await checkForEmbeddedJavascriptFromUrl('safe.pdf', s3Stub, loggerStub);
  });
});
