import {injectable} from '@loopback/context';
import {asWebhook<PERSON>and<PERSON>} from '../keys';

/**
 * Factory function to create a webhook handler decorator.
 *
 * @remarks
 * This function wraps the `injectable` decorator with the `as<PERSON>ebhook<PERSON>andler` binding key,
 * allowing webhook handler services to be registered in the LoopBack IoC container.
 *
 * @returns A class decorator that marks a service as a webhook handler
 */
export function webhookHandler() {
  return injectable(asWebhookHandler);
}
