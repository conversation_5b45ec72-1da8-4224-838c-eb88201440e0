import {Per<PERSON><PERSON><PERSON>, <PERSON>Dto} from '@local/core';
import {inject} from '@loopback/context';
import {Filter, CountSchema, Count, Where} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  param,
  Request,
  RestBindings,
} from '@loopback/rest';
import {
  CONTENT_TYPE,
  OPERATION_SECURITY_SPEC,
  STATUS_CODE,
} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {TenantBillingsView} from '../models';
import {SubscriptionProxyService} from '../services/proxies';

const basePath = '/tenant-billings';

/**
 * Controller for handling tenant billing-related operations.
 *
 * This controller provides endpoints to:
 * - Retrieve a list of tenant billings with optional filtering.
 * - Get the count of tenant billing records matching a filter.
 * - Fetch all possible billing statuses.
 *
 * Security:
 * - All endpoints require Bearer authentication.
 * - Permissions are enforced per endpoint using the `@authorize` decorator.
 *
 * Dependencies:
 * - Injects `SubscriptionProxyService` for business logic related to tenant billings.
 * - Accesses the current HTTP request to extract authorization headers.
 *
 * Endpoints:
 * - `GET /tenant-billings`: List tenant billings.
 * - `GET /tenant-billings/count`: Count tenant billings.
 * - `GET /tenant-billings/all-status`: List all billing statuses.
 */
export class TenantBillingsController {
  constructor(
    @inject('services.SubscriptionProxyService')
    private readonly subscriptionProxyService: SubscriptionProxyService,

    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {}

  @authorize({
    permissions: [PermissionKey.ViewTenantBillings],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of ConfigureDevice model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(TenantBillingsView, {
                includeRelations: true,
              }),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(TenantBillingsView) filter?: Filter<TenantBillingsView>,
  ): Promise<TenantBillingsView[]> {
    const token = this.request.headers.authorization ?? '';
    return this.subscriptionProxyService.getTenantBillings(token, filter);
  }

  @authorize({
    permissions: [PermissionKey.ViewTenantBillings],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/count`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Plan model count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.where(TenantBillingsView) where?: Where<TenantBillingsView>,
  ): Promise<Count> {
    return this.subscriptionProxyService.getTenantBillingsCount(
      this.request.headers.authorization ?? '',
      where,
    );
  }

  @authorize({
    permissions: [PermissionKey.ViewAllStatuses],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/all-status`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Object of all possible billing status',
        permissions: [PermissionKey.ViewAllStatuses],
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'object',
              items: getModelSchemaRef(StatusDto, {
                title: 'BillingStatusDto',
              }),
            },
          },
        },
      },
    },
  })
  async findAllStatus(): Promise<StatusDto> {
    return this.subscriptionProxyService.getAllBillingStatus(
      this.request.headers.authorization ?? '',
    );
  }
}
