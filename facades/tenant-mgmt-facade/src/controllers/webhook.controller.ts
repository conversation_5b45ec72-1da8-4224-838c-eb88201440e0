import {StripeEventPayload} from '@local/core';
import {service} from '@loopback/core';
import {post, requestBody} from '@loopback/rest';
import {rateLimitKeyGenPublic} from '@sourceloop/core';
import {authorize} from 'loopback4-authorization';
import {ratelimit} from 'loopback4-ratelimiter';
import {WebhookHandlerService} from '../services/webhook-handler.service';

/**
 * Controller responsible for handling incoming webhook events
 * from payment providers like Stripe.
 */
export class WebhookController {
  /**
   * Creates an instance of WebhookController.
   *
   * @param webhookHandlerService - Service responsible for processing webhook events
   */
  constructor(
    @service(WebhookHandlerService)
    private readonly webhookHandlerService: WebhookHandlerService,
  ) {}

  /**
   * Handles webhook events sent by the payment provider.
   *
   * @remarks
   * - Protected with rate limiting.
   * - Allows all permissions.
   * - Expects raw JSON payload.
   *
   * @param payload - The webhook payload received from Stripe or other providers
   * @returns A promise that resolves when the webhook is processed
   */
  @ratelimit(true, {
    max: parseInt(process.env.PUBLIC_API_MAX_ATTEMPTS ?? '5'),
    keyGenerator: rateLimitKeyGenPublic,
  })
  @authorize({
    permissions: ['*'],
  })
  @post('/webhook')
  async handleWebhook(
    @requestBody({
      description: 'Payment Provider webhook payload',
      required: true,
      content: {
        'application/json': {
          'x-parser': 'raw',
          schema: {type: 'object'},
        },
      },
    })
    payload: StripeEventPayload,
  ): Promise<void> {
    await this.webhookHandlerService.handle();
  }
}
