import {Permission<PERSON><PERSON>} from '@local/core';
import {inject} from '@loopback/context';
import {Filter} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  param,
  Request,
  RestBindings,
} from '@loopback/rest';
import {OPERATION_SECURITY_SPEC, STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {Currency} from '../models';
import {SubscriptionProxyService} from '../services/proxies';

const basePath = '/currencies';

/**
 * Controller to manage Currency resources.
 *
 * Provides endpoints to retrieve Currency entities.
 */
export class CurrencyController {
  /**
   * Creates an instance of CurrencyController.
   *
   * @param subscriptionProxyService - Service proxy to interact with subscription data.
   * @param request - The incoming HTTP request object.
   */
  constructor(
    @inject('services.SubscriptionProxyService')
    private readonly subscriptionProxyService: SubscriptionProxyService,

    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {}

  /**
   * Retrieves an array of Currency model instances.
   *
   * @param filter - Optional filter to query Currency entities.
   * @returns A promise that resolves to an array of Currency instances.
   *
   * @authorization Requires permission: ViewCurrency
   * @authentication Bearer token required
   */
  @authorize({
    permissions: [PermissionKey.ViewCurrency],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Currency model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(Currency, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(Currency) filter?: Filter<Currency>,
  ): Promise<Currency[]> {
    const token = this.request.headers.authorization ?? '';
    return this.subscriptionProxyService.getCurrencies(token, filter);
  }
}
