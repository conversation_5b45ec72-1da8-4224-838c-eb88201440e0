import {Permission<PERSON><PERSON>} from '@local/core';
import {inject} from '@loopback/context';
import {Filter} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  param,
  Request,
  RestBindings,
} from '@loopback/rest';
import {OPERATION_SECURITY_SPEC, STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {ConfigureDevice} from '../models';
import {SubscriptionProxyService} from '../services/proxies';

const basePath = '/configure-devices';

/**
 * Controller to manage ConfigureDevice resources.
 *
 * Provides endpoints to retrieve ConfigureDevice entities.
 */
export class ConfigureDeviceControllerController {
  /**
   * Creates an instance of ConfigureDeviceControllerController.
   *
   * @param subscriptionProxyService - Service proxy to interact with subscription data.
   * @param request - The incoming HTTP request object.
   */
  constructor(
    @inject('services.SubscriptionProxyService')
    private readonly subscriptionProxyService: SubscriptionProxyService,

    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {}

  /**
   * Retrieves an array of ConfigureDevice model instances.
   *
   * @param filter - Optional filter object to query ConfigureDevice entities.
   * @returns A promise that resolves to an array of ConfigureDevice instances.
   *
   * @authorization Requires permission: ViewConfigureDevices
   * @authentication Bearer token required
   */
  @authorize({
    permissions: [PermissionKey.ViewConfigureDevices],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of ConfigureDevice model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(ConfigureDevice, {
                includeRelations: true,
              }),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(ConfigureDevice) filter?: Filter<ConfigureDevice>,
  ): Promise<ConfigureDevice[]> {
    const token = this.request.headers.authorization ?? '';
    return this.subscriptionProxyService.getConfigureDevices(token, filter);
  }
}
