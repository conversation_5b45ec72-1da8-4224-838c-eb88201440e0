import {PermissionKey} from '@local/core';
import {inject} from '@loopback/context';
import {Filter} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  param,
  Request,
  RestBindings,
} from '@loopback/rest';
import {OPERATION_SECURITY_SPEC, STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PlanSizes} from '../models';
import {SubscriptionProxyService} from '../services/proxies';
import {IPlanSizes} from '../types';

const basePath = '/plan-storage';

/**
 * Controller to manage PlanSizes resources.
 *
 * Provides endpoints to retrieve PlanSizes entities.
 */
export class PlanStorageController {
  /**
   * Creates an instance of PlanStorageController.
   *
   * @param subscriptionProxyService - Service proxy to interact with subscription data.
   * @param request - The incoming HTTP request object.
   */
  constructor(
    @inject('services.SubscriptionProxyService')
    private readonly subscriptionProxyService: SubscriptionProxyService,

    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {}

  /**
   * Retrieves an array of PlanSizes model instances.
   *
   * @param filter - Optional filter object to query PlanSizes entities.
   * @returns A promise resolving to an array of PlanSizes instances.
   *
   * @authorization Requires permission: ViewPlanSizes
   * @authentication Bearer token required
   */
  @authorize({
    permissions: [PermissionKey.ViewPlanSizes],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of PlanSizes model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(PlanSizes, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(PlanSizes) filter?: Filter<IPlanSizes>,
  ): Promise<IPlanSizes[]> {
    const token = this.request.headers.authorization ?? '';
    return this.subscriptionProxyService.getStorageSize(token, filter);
  }
}
