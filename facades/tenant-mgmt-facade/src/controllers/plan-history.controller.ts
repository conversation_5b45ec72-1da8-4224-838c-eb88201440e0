import {Permission<PERSON><PERSON>} from '@local/core';
import {inject} from '@loopback/context';
import {Filter} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  param,
  Request,
  RestBindings,
} from '@loopback/rest';
import {OPERATION_SECURITY_SPEC, STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PlanHistory} from '../models';
import {SubscriptionProxyService} from '../services/proxies';
import {IPlanHistory} from '../types';

const basePath = '/plan-history';

/**
 * Controller to manage PlanHistory resources.
 *
 * Provides endpoints to retrieve PlanHistory entities.
 */
export class PlanHistoryController {
  /**
   * Creates an instance of PlanHistoryController.
   *
   * @param subscriptionProxyService - Service proxy to interact with subscription data.
   * @param request - The incoming HTTP request object.
   */
  constructor(
    @inject('services.SubscriptionProxyService')
    private readonly subscriptionProxyService: SubscriptionProxyService,

    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {}

  /**
   * Retrieves an array of PlanHistory model instances.
   *
   * @param filter - Optional filter object to query PlanHistory entities.
   * @returns A promise resolving to an array of PlanHistory instances.
   *
   * @authorization Requires permission: ViewPlanHistory
   * @authentication Bearer token required
   */
  @authorize({
    permissions: [PermissionKey.ViewPlanHistory],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of PlanHistory model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(PlanHistory, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(PlanHistory) filter?: Filter<IPlanHistory>,
  ): Promise<IPlanHistory[]> {
    const token = this.request.headers.authorization ?? '';
    return this.subscriptionProxyService.getPlanHistory(token, filter);
  }
}
