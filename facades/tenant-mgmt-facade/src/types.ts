import {PlanStatus, PlanTierType} from '@local/core';

export type FileMetadata = {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  bucket: string;
  key: string;
  acl: string;
  contentType: string;
  contentDisposition: string | null;
  contentEncoding: string | null;
  storageClass: string;
  serverSideEncryption: string | null;
  metadata: {
    fieldName: string;
  };
  location: string;
  etag: string;
  versionId?: string;
};

export type SubscriptionService = {
  handleWebhook(payload: unknown, signature: unknown): Promise<string>;
};
export interface IWebhookHandler {
  isApplicable(): boolean;
  handle(): Promise<void>;
}
export interface IBillingCycle {
  deleted: boolean;
  deletedOn?: string;
  deletedBy?: string;
  createdOn: string;
  modifiedOn?: string;
  createdBy: string;
  modifiedBy?: string;
  id: string;
  cycleName: string;
  duration: number;
  durationUnit: string;
  description: string;
}

export interface ICurrency {
  id: string;
  currencyCode: string;
  currencyName: string;
  symbol: string;
  country: string;
}

export interface IPlanHistory {
  deleted?: boolean;
  deletedOn?: Date;
  deletedBy?: string;
  createdOn?: Date;
  modifiedOn?: Date;
  createdBy?: string;
  modifiedBy?: string;
  id: string;
  price?: number;
  version: string;
  planId: string;
}

export interface IPlan {
  deleted?: boolean;
  deletedOn?: Date;
  deletedBy?: string;
  createdOn?: Date;
  modifiedOn?: Date;
  createdBy?: string;
  modifiedBy?: string;
  id: string;
  name: string;
  description?: string;
  tier: PlanTierType;
  size?: string;
  price: number;
  metaData?: object;
  status?: PlanStatus;
  version: string;
  allowedUnlimitedUsers: boolean;
  costPerUser: number;
  billingCycleId: string;
  currencyId: string;
  configureDeviceId: string;
  planSizeId?: string;
  planHistories?: IPlanHistory[]; // Optional in case not always populated
}

export interface IMetaData {
  pipelineName: string;
}

export interface IConfigureDevice {
  deleted?: boolean;
  deletedOn?: Date;
  deletedBy?: string;
  createdOn?: Date;
  modifiedOn?: Date;
  createdBy?: string;
  modifiedBy?: string;
  id: string;
  min: number;
  max: number;
}

export interface IPlanSizes {
  deleted?: boolean;
  deletedOn?: Date;
  deletedBy?: string;
  createdOn?: Date;
  modifiedOn?: Date;
  createdBy?: string;
  modifiedBy?: string;
  id: string;
  size: string;
  config?: object;
}
