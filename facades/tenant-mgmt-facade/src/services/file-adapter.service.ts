import {injectable, inject, service} from '@loopback/core';
import {StorageSource} from '@local/core';
import {HttpErrors} from '@loopback/rest';
import {AWSS3Bindings, S3WithSigner} from 'loopback4-s3';
import {GetObjectCommand} from '@aws-sdk/client-s3';
import path from 'path';
import {Readable} from 'stream';
import {PDFDocument, PDFName, PDFDict, PDFArray} from 'pdf-lib';
import {ILogger, LOGGER} from '@sourceloop/core';
import {FileMetadata} from '../types';
import {ALLOWED_FILE_EXTENSIONS, ALLOWED_MIME_TYPES} from '../constant';
import {S3HelperService} from './s3-helper.service';

const bucket = process.env.AWS_S3_BUCKET ?? '';
const errMsg = 'Uploaded file is not a valid PDF';

/**
 * Main function to verify a PDF file in S3 does not contain JavaScript triggers.
 *
 * @param fileKey - Key/path of the file in S3 bucket.
 * @param s3 - Instance of S3<PERSON><PERSON><PERSON><PERSON><PERSON> to retrieve the file.
 * @param logger - Optional logger to record operations and errors.
 * @returns Promise that resolves when the PDF is validated successfully.
 * @throws BadRequest if the PDF contains embedded JavaScript or can't be parsed.
 */
export async function checkForEmbeddedJavascriptFromUrl(
  fileKey: string,
  s3: S3WithSigner,
  logger?: ILogger,
): Promise<void> {
  const fileBuffer = await fetchFileBuffer(fileKey, s3, logger);
  const pdfDoc = await loadPdfDocument(fileBuffer, fileKey, logger);

  checkCatalogForJsTriggers(pdfDoc, fileKey, logger);
  checkPagesForJsTriggers(pdfDoc, fileKey, logger);
}

/**
 * Fetches the file buffer from S3 using the specified key.
 *
 * @param fileKey - The key of the file stored in S3.
 * @param s3 - An instance of S3WithSigner to send the request.
 * @param logger - Optional logger to capture errors.
 * @returns A Promise resolving to a Buffer containing the file contents.
 * @throws BadRequest error if the file stream is invalid or fetching fails.
 */
async function fetchFileBuffer(
  fileKey: string,
  s3: S3WithSigner,
  logger?: ILogger,
): Promise<Buffer> {
  const command = new GetObjectCommand({
    Bucket: bucket,
    Key: fileKey,
  });

  try {
    const {Body} = await s3.send(command);
    if (!Body || !(Body instanceof Readable)) {
      throw new HttpErrors.BadRequest('Invalid file stream from S3');
    }
    return await streamToBuffer(Body as Readable);
  } catch (err) {
    logger?.error('Error fetching file buffer from S3:', err);
    throw new HttpErrors.BadRequest('Invalid file stream from S3');
  }
}

/**
 * Loads a PDFDocument from a buffer.
 *
 * @param fileBuffer - The buffer containing PDF data.
 * @param fileKey - The key of the file used for logging context.
 * @param logger - Optional logger for error tracking.
 * @returns A Promise resolving to a loaded PDFDocument instance.
 * @throws BadRequest error if PDF parsing fails.
 */
async function loadPdfDocument(
  fileBuffer: Buffer,
  fileKey: string,
  logger?: ILogger,
): Promise<PDFDocument> {
  try {
    return await PDFDocument.load(fileBuffer, {ignoreEncryption: true});
  } catch (err) {
    logger?.error(`PDF parsing failed for ${fileKey}:`, err);
    throw new HttpErrors.BadRequest(errMsg);
  }
}

/**
 * Scans the catalog of a PDF document for JavaScript actions or embedded files.
 *
 * @param pdfDoc - The loaded PDFDocument.
 * @param fileKey - The file key used in logging.
 * @param logger - Optional logger for recording issues.
 * @throws BadRequest error if JS triggers or embedded files are found.
 */
function checkCatalogForJsTriggers(
  pdfDoc: PDFDocument,
  fileKey: string,
  logger?: ILogger,
): void {
  const catalog = pdfDoc.catalog;
  const jsTriggers = [
    catalog.lookup(PDFName.of('OpenAction')),
    catalog.lookup(PDFName.of('AA')),
    (catalog.lookup(PDFName.of('Names')) as PDFDict)?.lookup(
      PDFName.of('EmbeddedFiles'),
    ),
  ];

  if (jsTriggers.some(t => t)) {
    logger?.error(
      `Blocked: PDF contains JS or Embedded Files in catalog (${fileKey})`,
    );
    throw new HttpErrors.BadRequest(errMsg);
  }
}

/**
 * Scans individual pages of the PDF for JavaScript triggers.
 *
 * @param pdfDoc - The loaded PDFDocument.
 * @param fileKey - The file key used in logging.
 * @param logger - Optional logger for logging.
 * @throws BadRequest error if JS triggers or suspicious annotations are found.
 */
function checkPagesForJsTriggers(
  pdfDoc: PDFDocument,
  fileKey: string,
  logger?: ILogger,
): void {
  const pages = pdfDoc.getPages();
  for (const page of pages) {
    const pageDict = page.node as PDFDict;

    const pageAA = pageDict.lookup(PDFName.of('AA'));
    if (pageAA) {
      logger?.error(`Blocked: PDF contains page-level AA action in ${fileKey}`);
      throw new HttpErrors.BadRequest(errMsg);
    }

    const annots = pageDict.lookup(PDFName.of('Annots'));
    if (annots instanceof PDFArray) {
      checkAnnotationsForJsTriggers(annots, fileKey, logger);
    }
  }
}

/**
 * Scans annotations for JavaScript code or additional actions (AA).
 *
 * @param annots - PDF array of annotations.
 * @param fileKey - The file key used in logs.
 * @param logger - Optional logger for capturing errors.
 * @throws BadRequest error if any JS/AA annotations are detected.
 */
function checkAnnotationsForJsTriggers(
  annots: PDFArray,
  fileKey: string,
  logger?: ILogger,
): void {
  for (let i = 0; i < annots.size(); i++) {
    const annot = annots.lookup(i);
    if (annot instanceof PDFDict) {
      const annotAA = annot.lookup(PDFName.of('AA'));
      const annotJS = annot.lookup(PDFName.of('JS'));
      if (annotAA ?? annotJS) {
        logger?.error(
          `Blocked: PDF contains annotation with JS or AA in ${fileKey}`,
        );
        throw new HttpErrors.BadRequest(errMsg);
      }
    }
  }
}

/**
 * Converts a Readable stream into a Buffer.
 *
 * @param stream - Readable stream from S3 response.
 * @returns A Promise resolving to a Buffer of the stream contents.
 */
async function streamToBuffer(stream: Readable): Promise<Buffer> {
  const chunks: Buffer[] = [];
  for await (const chunk of stream) {
    chunks.push(typeof chunk === 'string' ? Buffer.from(chunk) : chunk);
  }
  return Buffer.concat(chunks);
}

/**
 * Validates if the file extension is in the allowed list.
 *
 * @param filename - Name of the file to validate.
 * @returns True if the extension is allowed, otherwise false.
 */
function isExtensionAllowed(filename: string): boolean {
  const ext = path.extname(filename).toLowerCase();
  return ALLOWED_FILE_EXTENSIONS.includes(ext);
}

/**
 * Validates if the MIME type is in the allowed list.
 *
 * @param mime - MIME type of the file.
 * @returns True if allowed, false otherwise.
 */
function isMimeTypeAllowed(mime: string): boolean {
  return ALLOWED_MIME_TYPES.includes(mime);
}

/**
 * Service class to handle S3 file operations like metadata fetching,
 * signed URL generation, and file validations.
 */
@injectable()
export class FileAdapterService {
  constructor(
    @inject(AWSS3Bindings.AwsS3Provider)
    private readonly s3: S3WithSigner,

    @service(S3HelperService)
    private readonly signedUrlService: S3HelperService,

    @inject(LOGGER.LOGGER_INJECT)
    private readonly logger: ILogger,
  ) {}

  /**
   * Generates file metadata and a signed download URL for the given file key.
   *
   * @param fileKey - Key of the file in S3.
   * @returns A Promise resolving to an object with metadata and signed URL.
   * @throws BadRequest if file key is missing or metadata fetch fails.
   */
  async generateFileInfoWithSignedUrl(fileKey: string) {
    if (!fileKey) {
      throw new HttpErrors.BadRequest();
    }

    const metadata = await this.s3.headObject({
      Bucket: process.env.AWS_S3_BUCKET ?? '',
      Key: fileKey,
    });

    const command = new GetObjectCommand({
      Bucket: process.env.AWS_S3_BUCKET ?? '',
      Key: fileKey,
    });

    const downloadUrl = await this.signedUrlService.getSignedUrl(command);

    return {
      metadata: metadata.Metadata,
      contentType: metadata.ContentType,
      size: metadata.ContentLength,
      lastModified: metadata.LastModified,
      downloadUrl,
    };
  }

  /**
   * Validates and transforms uploaded file metadata, ensuring no embedded JS,
   * and prepares a structured response.
   *
   * @param files - A single file or array of file metadata.
   * @returns A Promise resolving to an array of processed file info.
   * @throws BadRequest for invalid file extensions, MIME types, or JS content.
   */
  async generateFileResponse(files: FileMetadata[] | FileMetadata): Promise<
    {
      fileKey: string;
      originalName: string;
      source: StorageSource;
      size: number;
    }[]
  > {
    const fileArray = Array.isArray(files) ? files : [files];

    await Promise.all(
      fileArray.map(async file => {
        if (!isExtensionAllowed(file.originalname)) {
          throw new HttpErrors.BadRequest('Disallowed file extension');
        }

        if (!isMimeTypeAllowed(file.mimetype)) {
          throw new HttpErrors.BadRequest('Invalid file type');
        }

        await checkForEmbeddedJavascriptFromUrl(file.key, this.s3, this.logger);
      }),
    );

    return fileArray.map(file => ({
      fileKey: file.key,
      originalName: file.originalname,
      source: StorageSource.S3,
      size: file.size,
    }));
  }

  /**
   * Generates a signed S3 URL for the given file key.
   *
   * @param fileKey - The key of the file.
   * @param targetBucket - Optional bucket name. Uses default if not provided.
   * @returns A Promise resolving to a pre-signed URL.
   * @throws BadRequest if file key or bucket is not provided.
   */
  async generateSignedUrl(
    fileKey: string,
    targetBucket?: string,
  ): Promise<string> {
    if (!fileKey) {
      throw new HttpErrors.BadRequest();
    }

    const resolvedBucket = targetBucket ?? process.env.AWS_S3_BUCKET;
    if (!resolvedBucket) {
      throw new HttpErrors.BadRequest(
        'Bucket is not provided and no default is set',
      );
    }

    const command = new GetObjectCommand({
      Bucket: resolvedBucket,
      Key: fileKey,
    });

    return this.signedUrlService.getSignedUrl(command);
  }

  /**
   * Generates a LocalStack S3 pre-signed URL for viewing files inline (opens in new tab).
   * Specifically designed for LocalStack environment to avoid downloads and enable inline viewing.
   *
   * @param fileKey - The key of the file in S3.
   * @param originalName - Original filename with extension for content type detection.
   * @param targetBucket - Optional bucket name. Uses default if not provided.
   * @returns A Promise resolving to a pre-signed URL that opens files inline.
   * @throws BadRequest if file key or bucket is not provided.
   */
  async generateLocalStackViewUrl(
    fileKey: string,
    originalName?: string,
    targetBucket?: string,
  ): Promise<string> {
    if (!fileKey) {
      throw new HttpErrors.BadRequest('File key is required');
    }

    const resolvedBucket = targetBucket ?? process.env.AWS_S3_BUCKET;
    if (!resolvedBucket) {
      throw new HttpErrors.BadRequest(
        'Bucket is not provided and no default is set',
      );
    }

    // Use originalName for extension detection if available, otherwise fall back to fileKey
    const fileName = originalName ?? fileKey;
    const fileExtension = fileName.toLowerCase().split('.').pop();
    let contentType: string;
    const contentDisposition = 'inline';

    switch (fileExtension) {
      case 'pdf':
        contentType = 'application/pdf';
        break;
      case 'jpg':
      case 'jpeg':
        contentType = 'image/jpeg';
        break;
      case 'png':
        contentType = 'image/png';
        break;
      case 'gif':
        contentType = 'image/gif';
        break;
      case 'txt':
        contentType = 'text/plain';
        break;
      case 'html':
        contentType = 'text/html';
        break;
      default:
        contentType = 'application/octet-stream';
    }

    const command = new GetObjectCommand({
      Bucket: resolvedBucket,
      Key: fileKey,
      ResponseContentDisposition: contentDisposition,
      ResponseContentType: contentType,
    });

    return this.signedUrlService.getSignedUrl(command);
  }
}
