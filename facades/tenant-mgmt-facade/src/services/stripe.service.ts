import {RestBindings, Request, HttpErrors} from '@loopback/rest';
import {webhookHandler} from '../decorators/webhook-handler.decorator';
import {IWebhookHandler} from '../types';
import {inject} from '@loopback/context';
import {
  CryptoHelperService,
  InvoiceResponse,
  Notification,
  NotificationEventType,
  NotificationType,
  PermissionKey,
  StripeEventResponse,
  StripeEvents,
  TemplateService,
} from '@local/core';
import {LOGGER, ILogger} from '@sourceloop/core';
import {
  NotificationProxyService,
  SubscriptionProxyService,
  TenantMgmtProxyService,
} from './proxies';
import {service} from '@loopback/core';
import {ISubscription} from '@sourceloop/ctrl-plane-tenant-management-service';

const STRIPE_REQUEST_HEADER = 'stripe-signature';

/**
 * Webhook handler service implementation for processing Stripe events.
 *
 * @remarks
 * - Determines if the incoming webhook request is from Stripe.
 * - Handles Stripe events such as invoice creation and tenant provisioning.
 */
@webhookHandler()
export class StripeService implements IWebhookHandler {
  /**
   * Creates an instance of StripeService.
   *
   * @param request - Incoming HTTP request
   * @param subscriptionProxyService - Proxy service to handle subscription webhooks
   * @param notificationProxyService - Proxy service to handle notifications
   * @param tenantMgmtProxyService - Proxy service for tenant management
   * @param cryptoHelperService - Service to generate temporary tokens
   * @param templateService - Service to render notification templates
   * @param logger - Logger instance for logging
   */
  constructor(
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @inject('services.SubscriptionProxyService')
    private readonly subscriptionProxyService: SubscriptionProxyService,

    @inject(`services.NotificationProxyService`)
    private readonly notificationProxyService: NotificationProxyService,
    @inject('services.TenantMgmtProxyService')
    private readonly tenantMgmtProxyService: TenantMgmtProxyService,

    @service(CryptoHelperService)
    private readonly cryptoHelperService: CryptoHelperService,

    @service(TemplateService)
    private readonly templateService: TemplateService,
    @inject(LOGGER.LOGGER_INJECT)
    private readonly logger: ILogger,
  ) {}

  /**
   * Checks whether the incoming request is a Stripe webhook event.
   *
   * @returns `true` if the request contains a Stripe signature header, otherwise `false`
   */
  isApplicable(): boolean {
    this.logger.info(
      'Checking if stripe webhook is applicable ' +
        this.request.headers[STRIPE_REQUEST_HEADER],
    );

    if (this.request.headers[STRIPE_REQUEST_HEADER]) return true;
    return false;
  }

  /**
   * Handles the Stripe webhook event.
   *
   * @remarks
   * - Delegates processing to the subscription proxy service.
   * - If the event is `INVOICE_CREATED`, it sends an invoice payment request notification.
   * - If provisioning is required, it triggers tenant provisioning.
   * - Ignores other events.
   *
   * @throws {@link HttpErrors.InternalServerError} If the required notification template is missing
   */
  async handle(): Promise<void> {
    const webhookResponse: string =
      await this.subscriptionProxyService.handleWebhook(
        this.request.body,
        this.request.headers[STRIPE_REQUEST_HEADER],
      );

    const parsedResponse = JSON.parse(webhookResponse) as StripeEventResponse<
      InvoiceResponse | undefined
    >;

    if (
      parsedResponse.event === StripeEvents.INVOICE_CREATED &&
      parsedResponse.success &&
      parsedResponse.sendEmail
    ) {
      const token = this.cryptoHelperService.generateTempToken({
        userTenantId: process.env.SYSTEM_USER_TENANT_ID,
        tenantId: process.env.SYSTEM_USER_TENANT_ID,
        defaultTenantId: process.env.SYSTEM_USER_TENANT_ID,
        permissions: [
          PermissionKey.CreateNotification,
          PermissionKey.ViewNotificationTemplate,
        ],
      });

      // Fetch email template
      const template = await this.notificationProxyService.getTemplateByName(
        NotificationEventType.InvoicePaymentRequest,
        NotificationType.EMAIL,
        token,
      );

      if (!template) {
        throw new HttpErrors.InternalServerError(
          'Notification template for forget password not found',
        );
      }

      // Generate email body using template
      const emailBody = this.templateService.generateEmail(template.body, {
        paymentLink: parsedResponse.message ?? '',
      });

      const notification: Notification = new Notification({
        subject: template.subject,
        body: emailBody,
        receiver: {
          to: [
            {
              id: parsedResponse.userId ?? '',
              toEmail: parsedResponse.email,
            },
          ],
        },
        type: 1,
        sentDate: new Date(),
        options: {
          fromEmail: process.env.NOTIFICATION_FROM_EMAIL,
        },
      });

      await this.notificationProxyService.createNotification(
        token,
        notification,
      );
    } else if (parsedResponse.isProvisionRequired) {
      const subscription = JSON.parse(
        parsedResponse.message ?? '',
      ) as ISubscription;

      console.log('Final Subscription DTO', subscription); // NOSONAR

      const token = this.cryptoHelperService.generateTempToken({
        userTenantId: process.env.SYSTEM_USER_TENANT_ID,
        tenantId: process.env.SYSTEM_USER_TENANT_ID,
        defaultTenantId: process.env.SYSTEM_USER_TENANT_ID,
        permissions: [PermissionKey.ProvisionTenant],
      });

      const sdto: ISubscription = {
        id: subscription.id,
        subscriberId: subscription.subscriberId,
        startDate: subscription.startDate,
        endDate: subscription.endDate,
        planId: subscription.planId,
        status: subscription.status,
        plan: {
          ...subscription.plan,
          id: subscription.planId ?? 'NA',
          name: subscription.plan?.name ?? 'NA',
          description: subscription.plan?.description ?? 'NA',
          price: subscription.plan?.price ?? 0,
          currencyId: subscription.plan?.currencyId ?? 'NA',
          billingCycleId: subscription.plan?.billingCycleId ?? 'NA',
          metaData: {
            pipelineName: subscription.plan?.tier.toUpperCase() ?? 'NA',
          },
          tier: subscription.plan?.tier.toUpperCase() ?? 'NA',
        },
      };

      await this.tenantMgmtProxyService.provisionTenant(
        `Bearer ${token}`,
        subscription.subscriberId,
        sdto,
      );

      console.log('provisioning successfull'); // NOSONAR
    } else {
      // No operation for other events
    }
  }
}
