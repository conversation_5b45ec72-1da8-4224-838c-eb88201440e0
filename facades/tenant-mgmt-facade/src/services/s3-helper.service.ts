import {injectable, inject} from '@loopback/core';
import {GetObjectCommand} from '@aws-sdk/client-s3';
import {getSignedUrl} from '@aws-sdk/s3-request-presigner';
import {AWSS3Bindings, S3WithSigner} from 'loopback4-s3';

@injectable()
export class S3HelperService {
  constructor(
    @inject(AWSS3Bindings.AwsS3Provider)
    private readonly s3: S3WithSigner,
  ) {}

  async getSignedUrl(command: GetObjectCommand): Promise<string> {
    return getSignedUrl(this.s3, command, {expiresIn: 3600});
  }
}
