import {Per<PERSON><PERSON><PERSON>, <PERSON>Dto} from '@local/core';
import {BindingScope, inject, injectable} from '@loopback/core';
import {Count, Filter, Where} from '@loopback/repository';
import {HttpErrors, Request, RestBindings} from '@loopback/rest';
import {<PERSON>og<PERSON>, LOGGER} from '@sourceloop/core';
import {
  CreateTenantDTO,
  CustomerDto,
  FileObject,
  Tenant,
  TenantOnboardDTO,
  VerifyKeyDto,
} from '../models';
import {FileMetadata} from '../types';
import {FileAdapterService} from './file-adapter.service';
import {SubscriptionProxyService, TenantMgmtProxyService} from './proxies';
import {
  CryptoHelperService,
  SubscriptionStatus,
} from '@sourceloop/ctrl-plane-tenant-management-service';

const TEMP_TOKEN_EXPIRATION = 30000; // 30 seconds
@injectable({scope: BindingScope.TRANSIENT})
/**
 * A helper service to manage tenant onboarding operations such as key verification,
 * tenant creation, and file handling.
 */
export class TenantHelperService {
  token: string;

  /**
   * Constructs a new instance of the TenantHelperService.
   *
   * @param request - The current HTTP request, used to extract the authorization token.
   * @param fileAdapterService - Service to manage file operations.
   * @param subscriptionProxyService - Proxy service to interact with the subscription service.
   * @param tenantMgmtProxyService - Proxy service to interact with the tenant management service.
   * @param logger - Logger for logging errors or messages.
   *
   * @throws {HttpErrors.Unauthorized} If the Authorization header is missing.
   */
  constructor(
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @inject('services.FileAdapterService')
    private readonly fileAdapterService: FileAdapterService,
    @inject('services.TenantMgmtProxyService')
    private readonly tenantMgmtProxyService: TenantMgmtProxyService,
    @inject('services.SubscriptionProxyService')
    private readonly subscriptionProxyService: SubscriptionProxyService,
    @inject('services.CryptoHelperService')
    private readonly cryptoHelperService: CryptoHelperService,
    @inject(LOGGER.LOGGER_INJECT)
    private readonly logger: ILogger,
  ) {
    if (this.request.headers.authorization) {
      this.token = this.request.headers.authorization;
    } else {
      throw new HttpErrors.Unauthorized();
    }
  }

  /**
   * Verifies if a given key is valid for tenant onboarding.
   *
   * @param body - The DTO containing key details to verify.
   * @returns A promise resolving to the result of the key verification.
   */
  async verifyKey(body: VerifyKeyDto) {
    return this.tenantMgmtProxyService.verifyKey(this.token, body);
  }

  private _verifyPayload(dto: CreateTenantDTO) {
    if (typeof dto.contact === 'string') {
      try {
        dto.contact = JSON.parse(dto.contact);
      } catch (error) {
        this.logger.error(JSON.stringify(error));
        throw new HttpErrors.BadRequest('Invalid JSON format for contact');
      }
    }

    if (dto.contact) {
      if (dto.contact.phoneNumber === '') {
        dto.contact.phoneNumber = undefined;
      }

      if (dto.contact.designation === '') {
        dto.contact.designation = undefined;
      }
    }
  }

  /**
   * Creates a new tenant using the provided onboarding data.
   *
   * @param dto - The CreateTenantDTO containing tenant details.
   * @returns A promise resolving to the created tenant object.
   *
   * @throws {HttpErrors.BadRequest} If the contact information is not valid JSON.
   */
  async createTenant(dto: CreateTenantDTO) {
    this._verifyPayload(dto);
    const selectedPlan = await this.subscriptionProxyService.findPlanById(
      this.token,
      dto.planId,
      {
        include: [
          {
            relation: 'currency',
          },
          {
            relation: 'billingCycle',
          },
        ],
      },
    );
    if (!selectedPlan) {
      throw new Error('selected plan does not exist');
    }
    const domains = [process.env.DOMAIN as string];

    const {files, numberOfUsers, totalCost, ...rest} = dto;
    console.info('Number of Users:', numberOfUsers);
    let fileRes: FileObject[] | undefined;

    if (dto.files) {
      fileRes = await this.fileAdapterService.generateFileResponse(
        files as unknown as FileMetadata | FileMetadata[],
      );
    }

    const newDto: TenantOnboardDTO = new TenantOnboardDTO({
      ...rest,
      domains,
      ...(fileRes ? {files: fileRes} : {}),
    });

    const tenant = await this.tenantMgmtProxyService.createTenant(
      this.token,
      newDto,
    );

    await this.subscriptionProxyService.createCustomer(
      this.token,
      {
        firstName: dto.contact.firstName,
        lastName: dto.contact.lastName,
        email: dto.contact.email,
        phone: dto.contact.phoneNumber,
        name: dto.name,
      } as CustomerDto,
      tenant.id,
    );
    const price = await this.subscriptionProxyService.createPrice(this.token, {
      product: selectedPlan.productRefId,
      recurring: {
        interval: selectedPlan.billingCycle?.durationUnit,
        //eslint-disable-next-line @typescript-eslint/naming-convention
        interval_count: selectedPlan.billingCycle?.duration,
      },
      currency: selectedPlan.currency?.currencyCode.toLowerCase(),
      active: true,
      //eslint-disable-next-line @typescript-eslint/naming-convention
      unit_amount: Math.round(dto.totalCost * 100), // Convert to cents
    });

    await this._createSubscription(
      dto.planId,
      tenant.id,
      dto.numberOfUsers,
      totalCost,
      price.id,
    );

    return tenant;
  }

  private async _createSubscription(
    planId: string,
    userId: string,
    numberOfUsers?: number,
    totalCost?: number,
    priceRefId?: string,
    invoiceId?: string | undefined,
  ) {
    const token = this.cryptoHelperService.generateTempToken(
      {
        id: userId,
        userTenantId: userId,
        permissions: [
          PermissionKey.ViewSubscription,
          PermissionKey.ViewPlan,
          PermissionKey.CreateSubscription,
          PermissionKey.CreateInvoice,
          '7029', // view plan sizes
          '7033', // view plan features
        ],
      },
      TEMP_TOKEN_EXPIRATION,
    );

    const createdSubscription =
      await this.subscriptionProxyService.createSubscriptions(
        `Bearer ${token}`,
        {
          planId,
          subscriberId: userId,
          status: SubscriptionStatus.PENDING,
          numberOfUsers: numberOfUsers
            ? parseInt(numberOfUsers as unknown as string, 10)
            : undefined,
          totalCost: totalCost
            ? parseFloat(totalCost as unknown as string)
            : undefined,
          priceRefId,
        },
      );
    return createdSubscription;
  }

  /**
   * Counts the number of tenants that match the specified criteria.
   *
   * @param where - Optional filter criteria to determine which tenants to count.
   * @returns A promise that resolves to the count of tenants matching the filter.
   */
  countTenants(where?: Where<Tenant>): Promise<Count> {
    return this.tenantMgmtProxyService.getTenantCount(this.token, where);
  }

  /**
   * Retrieves a list of tenants based on the provided filter criteria.
   *
   * @param filter - Optional filter object to specify query parameters for tenant retrieval.
   * @returns A promise that resolves to the list of tenants matching the filter.
   */
  async getTenants(filter?: Filter<Tenant>) {
    return this.tenantMgmtProxyService.getTenant(this.token, filter);
  }

  /**
   * Retrieves tenant details by tenant ID.
   *
   * @param tenantId - The ID of the tenant to retrieve details for.
   * @returns A promise that resolves to an object containing tenant details, contact info, files, address, and plan.
   */
  async getTenantDetails(tenantId: string) {
    const tenant = await this.tenantMgmtProxyService.getTenantById(
      this.token,
      tenantId,
    );

    // Extract tenant admin contact (assuming first contact is admin)
    const tenantAdmin =
      tenant.contacts && tenant.contacts.length > 0 ? tenant.contacts[0] : {};

    // Extract files and generate pre-signed URLs
    const rawFiles = Array.isArray(tenant.files) ? tenant.files : [];
    this.logger.info(`Found ${rawFiles.length} files for tenant ${tenantId}`);

    if (rawFiles.length > 0) {
      this.logger.info(
        `File keys found: ${rawFiles.map(f => f.fileKey).join(', ')}`,
      );
    }

    const filesWithSignedUrls = await Promise.all(
      rawFiles.map(async file => {
        try {
          this.logger.info(
            `Generating LocalStack view URL for file: ${file.originalName} (fileKey: ${file.fileKey})`,
          );
          const signedUrl =
            await this.fileAdapterService.generateLocalStackViewUrl(
              file.fileKey,
              file.originalName,
            );
          this.logger.info(
            `Generated LocalStack view URL: ${signedUrl?.substring(0, 100)}...`,
          );
          return {
            ...file,
            signedUrl: signedUrl,
          };
        } catch (error) {
          this.logger.error(
            `Error generating LocalStack view URL for file ${file.originalName}:`,
            error,
          );
          return {
            ...file,
            signedUrl: null,
          };
        }
      }),
    );

    // Extract address details - LoopBack populates belongsTo relations with the property name
    // Use type assertion to access the dynamic property
    const tenantWithRelations = tenant as Tenant & {address?: object};
    const address = tenantWithRelations.address ?? {};

    // Create a clean tenant details object without nested relations
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const {contacts, files: tenantFiles, ...tenantDetails} = tenant;

    return {
      tenantDetails: tenantDetails,
      contact: tenantAdmin,
      files: filesWithSignedUrls,
      address: address,
      plan: {}, // Empty object as requested
    };
  }

  /**
   * Retrieves all possible tenant statuses.
   *
   * @returns A promise that resolves to an array of TenantStatusDto objects.
   */
  async getAllStatuses(): Promise<StatusDto[]> {
    return this.tenantMgmtProxyService.getAllTenantStatus(this.token);
  }
}
