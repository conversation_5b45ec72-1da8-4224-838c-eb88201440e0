import {IFileLimitsGetter, MulterUploadOptions} from '@sourceloop/file-utils';
import {injectable, BindingScope} from '@loopback/context';
import {AnyObject} from '@loopback/repository';
import {UPLOAD_FILE_SIZE, MAX_FILES} from '../constant';

@injectable({scope: BindingScope.SINGLETON})
export class FileUploadLimitsService implements IFileLimitsGetter {
  async get(): Promise<MulterUploadOptions<AnyObject>> {
    const sizeLimits = {
      fileSize: UPLOAD_FILE_SIZE,
      files: MAX_FILES,
    };
    return {sizeLimits};
  }
}
