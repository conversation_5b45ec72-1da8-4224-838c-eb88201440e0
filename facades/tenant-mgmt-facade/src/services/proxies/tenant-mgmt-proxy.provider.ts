import {Provider, inject} from '@loopback/core';
import {getService} from '@loopback/service-proxy';
import {TenantMgmtServiceDataSource} from '../../datasources';
import {
  KeySuggestionDto,
  Tenant,
  TenantOnboardDTO,
  VerifyKeyDto,
} from '../../models';
import {Count, Filter, Where} from '@loopback/repository';
import {StatusDto} from '@local/core';
import {ISubscription} from '@sourceloop/ctrl-plane-tenant-management-service';

/**
 * Interface for proxy-based interaction with the Tenant Management microservice.
 */
export interface TenantMgmtProxyService {
  /**
   * Creates a new tenant using the provided onboarding payload.
   *
   * @param token - Authorization token for the request.
   * @param payload - The data required to onboard a new tenant.
   * @returns A Promise resolving to the created Tenant instance.
   */
  createTenant(token: string, payload: TenantOnboardDTO): Promise<Tenant>;

  /**
   * Verifies a tenant key and provides suggestions if needed.
   *
   * @param token - Authorization token for the request.
   * @param payload - The key verification input payload.
   * @returns A Promise resolving to a KeySuggestionDto, including validation result or suggestions.
   */
  verifyKey(token: string, payload: VerifyKeyDto): Promise<KeySuggestionDto>;

  /**
   * Retrieves a tenant by its ID.
   *
   * @param token - Authorization token for the request.
   * @param id - The ID of the tenant to retrieve.
   * @returns A Promise resolving to the Tenant instance with contact details.
   */
  getTenantById(token: string, id: string): Promise<Tenant>;

  getTenant(token: string, filter?: Filter<Tenant>): Promise<Tenant[]>;
  getTenantCount(token: string, where?: Where<Tenant>): Promise<Count>;

  /**
   * Retrieves the status of all tenants.
   *
   * @param token - Authorization token for the request.
   * @returns A Promise resolving to an array of TenantStatusDto objects.
   */
  getAllTenantStatus(token: string): Promise<StatusDto[]>;

  provisionTenant(
    token: string,
    id: string,
    payload: ISubscription,
  ): Promise<void>;
}

/**
 * Provider class that supplies a proxy implementation of the TenantMgmtProxyService,
 * backed by the configured TenantMgmtServiceDataSource.
 */
export class TenantMgmtProxyServiceProvider
  implements Provider<TenantMgmtProxyService>
{
  /**
   * Creates an instance of the provider.
   *
   * @param dataSource - The injected data source used to create the proxy service.
   */
  constructor(
    @inject('datasources.TenantMgmtService')
    protected dataSource: TenantMgmtServiceDataSource,
  ) {}

  /**
   * Returns the proxy service used to interact with the tenant management microservice.
   *
   * @returns A Promise that resolves to the TenantMgmtProxyService instance.
   */
  value(): Promise<TenantMgmtProxyService> {
    return getService(this.dataSource);
  }
}
