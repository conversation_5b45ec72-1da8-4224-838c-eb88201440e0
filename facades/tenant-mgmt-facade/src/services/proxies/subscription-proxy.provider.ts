import {<PERSON>Dto, StatusDto} from '@local/core';
import {inject, Provider} from '@loopback/core';
import {Count, Filter, FilterExcludingWhere, Where} from '@loopback/repository';
import {getService} from '@loopback/service-proxy';
import {SubscriptionServiceDataSource} from '../../datasources';
import {
  ConfigureDevice,
  Currency,
  CustomerDto,
  Invoice,
  Plan,
  PlanHistory,
  Subscription,
  TenantBillingsView,
} from '../../models';
import {IBillingCycle, IPlan, IPlanSizes} from '../../types';

/**
 * Interface for interacting with the Subscription Service via proxy.
 */
export interface SubscriptionProxyService {
  /**
   * Fetches a subscription plan by its ID from the remote service.
   *
   * @param token - Authorization token used for the request.
   * @param id - The unique identifier of the subscription plan.
   * @param filter - Optional LoopBack filter to include additional query options.
   * @returns A Promise resolving to the subscription plan (`IPlan`).
   */
  findPlanById(
    token: string,
    id: string,
    filter?: FilterExcludingWhere<Plan>,
  ): Promise<Plan>;

  getConfigureDevices(
    token: string,
    filter?: Filter<ConfigureDevice>,
  ): Promise<ConfigureDevice[]>;
  getStorageSize(
    token: string,
    filter?: Filter<IPlanSizes>,
  ): Promise<IPlanSizes[]>;
  getSubscriptionTenure(
    token: string,
    filter?: Filter<IBillingCycle>,
  ): Promise<IBillingCycle[]>;
  getPlans(token: string, filter?: Filter<IPlan>): Promise<IPlan[]>;
  createPlan(token: string, body: Partial<IPlan>): Promise<IPlan>;
  createSubscriptions(
    token: string,
    body: Partial<Subscription>,
  ): Promise<Subscription>;

  getCurrencies(token: string, filter?: Filter<Currency>): Promise<Currency[]>;
  getPlansCount(token: string, where?: Where<Plan>): Promise<Count>;
  getPlanHistory(
    token: string,
    filter?: Filter<PlanHistory>,
  ): Promise<PlanHistory[]>;
  getAllPlanStatus(token: string): Promise<StatusDto>;
  updatePlanById(token: string, id: string, body: Partial<Plan>): Promise<Plan>;
  createPlanHistory(
    token: string,
    body: Partial<PlanHistory>,
  ): Promise<PlanHistory>;
  createPrice(token: string, body: Partial<PriceDto>): Promise<PriceDto>;
  createCustomer(
    token: string,
    customerDto: Omit<CustomerDto, 'id'>,
    tenantId: string,
  ): Promise<CustomerDto>;
  getTenantBillings(
    token: string,
    filter?: Filter<TenantBillingsView>,
  ): Promise<TenantBillingsView[]>;
  getTenantBillingsCount(
    token: string,
    where?: Where<TenantBillingsView>,
  ): Promise<Count>;
  getAllBillingStatus(token: string): Promise<StatusDto>;
  getInvoices(
    token: string,
    filter?: Filter<Invoice>,
  ): Promise<Invoice[]>;
  getInvoicesCount(
    token: string,
    where?: Where<Invoice>,
  ): Promise<Count>;

  handleWebhook(payload: unknown, signature: unknown): Promise<string>;
}

/**
 * Provider that supplies a proxy-based implementation of SubscriptionProxyService
 * using the configured `SubscriptionServiceDataSource`.
 */
export class SubscriptionProxyServiceProvider
  implements Provider<SubscriptionProxyService>
{
  /**
   * Constructs a new SubscriptionProxyServiceProvider.
   *
   * @param dataSource - The injected SubscriptionServiceDataSource used to proxy the service.
   */
  constructor(
    @inject('datasources.SubscriptionService')
    protected dataSource: SubscriptionServiceDataSource,
  ) {}

  /**
   * Returns the proxy service instance for communicating with the subscription microservice.
   *
   * @returns A Promise resolving to the SubscriptionProxyService implementation.
   */
  value(): Promise<SubscriptionProxyService> {
    return getService(this.dataSource);
  }
}
