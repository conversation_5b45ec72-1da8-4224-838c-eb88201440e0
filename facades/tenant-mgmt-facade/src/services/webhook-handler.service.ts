import {extensionPoint, extensions, Getter} from '@loopback/core';
import {WebhookHandlerEP} from '../keys';
import {IWebhookHandler} from '../types';
import {HttpErrors} from '@loopback/rest';

/**
 * Service responsible for managing and delegating webhook events
 * to the appropriate registered handlers.
 *
 * @remarks
 * - Uses the `WebhookHandlerEP` extension point.
 * - Resolves all available `IWebhookHandler` implementations.
 * - Executes the handler that declares itself as applicable.
 */
@extensionPoint(WebhookHandlerEP.key)
export class WebhookHandlerService {
  /**
   * Creates an instance of WebhookHandlerService.
   *
   * @param getHandlers - Getter function that retrieves all registered webhook handlers
   */
  constructor(
    @extensions()
    private readonly getHandlers: Getter<IWebhookHandler[]>,
  ) {}

  /**
   * Delegates the webhook request to the applicable handler.
   *
   * @throws {@link HttpErrors.UnprocessableEntity} If no applicable handler is found
   */
  async handle() {
    const handlers = await this.getHandlers();
    const handler = handlers.find(h => h.isApplicable());
    if (handler) {
      await handler.handle();
    } else {
      throw new HttpErrors.UnprocessableEntity('Invalid type of webhook');
    }
  }
}
