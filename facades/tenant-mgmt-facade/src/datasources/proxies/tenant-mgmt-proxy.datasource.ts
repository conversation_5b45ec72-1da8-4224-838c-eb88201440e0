import {inject, lifeCycleObserver, LifeCycleObserver} from '@loopback/core';
import {juggler} from '@loopback/repository';
import {CONTENT_TYPE} from '@sourceloop/core';

const tokenKey = '{token}';
const config = {
  name: 'TenantMgmtService',
  connector: 'rest',
  baseURL: '',
  crud: false,
  options: {
    baseUrl: process.env.TENANT_MGMT_URL,
    headers: {
      accept: CONTENT_TYPE.JSON,
      ['content-type']: CONTENT_TYPE.JSON,
    },
  },
  operations: [
    {
      template: {
        method: 'POST',
        url: '/tenants',
        headers: {
          Authorization: tokenKey,
        },
        body: '{body}',
      },
      functions: {
        createTenant: ['token', 'body'],
      },
    },
    {
      template: {
        method: 'POST',
        url: '/tenants/verify-key',
        headers: {
          Authorization: tokenKey,
        },
        body: '{body}',
      },
      functions: {
        verifyKey: ['token', 'body'],
      },
    },
    {
      template: {
        method: 'GET',
        url: '/tenants/{id}',
        headers: {
          Authorization: tokenKey,
        },
        query: {
          filter: '{filter}',
        },
      },
      functions: {
        getTenantById: ['token', 'id', 'filter'],
      },
    },
    {
      template: {
        method: 'GET',
        url: '/contacts',
        headers: {
          Authorization: tokenKey,
        },
        query: {
          filter: '{filter}',
        },
      },
      functions: {
        getContacts: ['token', 'filter'],
      },
    },
    {
      template: {
        method: 'GET',
        url: '/tenants/all-status',
        headers: {
          Authorization: tokenKey,
        },
      },
      functions: {
        getAllTenantStatus: ['token'],
      },
    },
    {
      template: {
        method: 'GET',
        url: '/tenants',
        headers: {
          Authorization: tokenKey,
        },
        query: {
          filter: '{filter}',
        },
      },
      functions: {
        getTenant: ['token', 'filter'],
      },
    },
    {
      template: {
        method: 'GET',
        url: '/tenants/count',
        headers: {
          Authorization: tokenKey,
        },
        query: {
          where: '{where}',
        },
      },
      functions: {
        getTenantCount: ['token', 'where'],
      },
    },
    {
      template: {
        method: 'POST',
        url: '/tenants/{id}/provision',
        headers: {
          Authorization: tokenKey,
        },
        body: '{body}',
      },
      functions: {
        provisionTenant: ['token', 'id', 'body'],
      },
    },
  ],
};

// Observe application's life cycle to disconnect the datasource when
// application is stopped. This allows the application to be shut down
// gracefully. The `stop()` method is inherited from `juggler.DataSource`.
// Learn more at https://loopback.io/doc/en/lb4/Life-cycle.html
@lifeCycleObserver('datasource')
export class TenantMgmtServiceDataSource
  extends juggler.DataSource
  implements LifeCycleObserver
{
  static readonly dataSourceName = 'TenantMgmtService';
  static readonly defaultConfig = config;

  constructor(
    @inject('datasources.config.TenantMgmtService', {optional: true})
    dsConfig: object = config,
  ) {
    super(dsConfig);
  }
}
