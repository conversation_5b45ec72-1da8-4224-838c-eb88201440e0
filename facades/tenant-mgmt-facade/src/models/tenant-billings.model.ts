import {InvoiceStatus} from '@local/core';
import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {BillingCustomer} from '@sourceloop/ctrl-plane-subscription-service';

/**
 * Represents a view of tenant billing information, mapped to the 'tenant_billings_view' model.
 *
 * This model includes details about invoices, billing customers, amounts, taxes, discounts,
 * due dates, and tenant information. It extends the UserModifiableEntity to include
 * user modification tracking fields.
 *
 * @property {string} id - Unique identifier for the billing view (auto-generated).
 * @property {string} invoiceId - Identifier for the invoice (required).
 * @property {InvoiceStatus} [invoiceStatus] - Status of the invoice or payment.
 * @property {string} billingCustomerId - Reference to the billing customer (foreign key).
 * @property {number} amount - Total amount for the invoice (required).
 * @property {number} tax - Tax amount applied to the invoice (required).
 * @property {number} [discount] - Discount applied to the invoice (optional).
 * @property {string} dueDate - Due date for the invoice (required).
 * @property {string} customerId - Identifier for the customer/tenant (required).
 * @property {string} tenantId - Identifier for the tenant (required).
 * @property {string} tenantName - Name of the tenant (required).
 *
 * @extends UserModifiableEntity
 */
@model({
  name: 'tenant_billings_view',
})
export class TenantBillingsView extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    name: 'invoice_id',
    required: true,
  })
  invoiceId: string;

  @property({
    type: 'string',
    name: 'invoice_status',
    description: 'payment or invoice status',
  })
  invoiceStatus?: InvoiceStatus;

  @belongsTo(
    () => BillingCustomer,
    {keyTo: 'billingCustomerId'},
    {name: 'billing_customer_id'},
  )
  billingCustomerId: string;

  @property({
    type: 'number',
    required: true,
  })
  amount: number;

  @property({
    type: 'number',
    required: true,
  })
  tax: number;

  @property({
    type: 'number',
    required: false,
  })
  discount: number;

  @property({
    name: 'due_date',
    type: 'string',
    required: true,
    description: 'due date for the invoice',
  })
  dueDate: string;

  @property({
    type: 'string',
    name: 'tenant_id',
    required: true,
  })
  customerId: string;

  @property({
    type: 'string',
    name: 'tenant_id',
    required: true,
  })
  tenantId: string;

  @property({
    type: 'string',
    name: 'tenant_name',
    required: true,
  })
  tenantName: string;

  constructor(data?: Partial<TenantBillingsView>) {
    super(data);
  }
}
