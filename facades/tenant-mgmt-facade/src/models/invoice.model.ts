import {InvoiceStatus} from '@local/core';
import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {BillingCustomer} from '@sourceloop/ctrl-plane-subscription-service';

/**
 * Represents an invoice entity in the system.
 *
 * Inherits auditing fields (createdBy, modifiedBy, etc.)
 * from {@link UserModifiableEntity}.
 */
@model({
  name: 'invoice',
})
export class Invoice extends UserModifiableEntity {
  /**
   * System-generated unique identifier of the invoice record.
   */
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  /**
   * External invoice ID from the billing provider (e.g., Stripe).
   */
  @property({
    type: 'string',
    name: 'invoice_id',
    required: true,
  })
  invoiceId: string;

  /**
   * Current status of the invoice (e.g., paid, open, void).
   */
  @property({
    type: 'string',
    name: 'invoice_status',
    description: 'payment or invoice status',
  })
  invoiceStatus?: InvoiceStatus;

  /**
   * Identifier of the associated billing customer.
   */
  @belongsTo(
    () => BillingCustomer,
    {keyTo: 'billingCustomerId'},
    {name: 'billing_customer_id'},
  )
  billingCustomerId: string;

  /**
   * Total invoice amount before tax and discounts.
   */
  @property({
    type: 'number',
    required: true,
  })
  amount: number;

  /**
   * Tax amount applied to the invoice.
   */
  @property({
    type: 'number',
    required: true,
  })
  tax: number;

  /**
   * Discount applied to the invoice, if any.
   */
  @property({
    type: 'number',
    required: false,
  })
  discount: number;

  /**
   * Due date of the invoice, in ISO string format.
   */
  @property({
    name: 'due_date',
    type: 'string',
    required: true,
    description: 'due date for the invoice',
  })
  dueDate: string;

  /**
   * Creates an instance of Invoice.
   *
   * @param data - Partial data to initialize the invoice
   */
  constructor(data?: Partial<Invoice>) {
    super(data);
  }
}
