import {model, Model, property} from '@loopback/repository';
@model({
  description: 'model describing payload used to edit plan',
})
export class EditPlanDto extends Model {
  /**
   * Price of the plan.
   */
  @property({
    type: 'number',
    required: true,
  })
  price: number;

  /**
   * Indicates if the plan allows unlimited users.
   */
  @property({
    type: 'boolean',
    description: 'Indicates if the plan allows unlimited users.',
    name: 'allowed_unlimited_users',
  })
  allowedUnlimitedUsers?: boolean;

  /**
   * Price added per user for the plan, if applicable.
   */
  @property({
    type: 'number',
    description: 'price added per user for the plan.',
    name: 'cost_per_user',
  })
  costPerUser?: number;
  constructor(data?: Partial<EditPlanDto>) {
    super(data);
  }
}
