import {model, Model, property} from '@loopback/repository';
@model({
  description:
    'model describing payload used to send resof verifying key for a tenant',
})
export class KeySuggestionDto extends Model {
  @property({type: 'boolean', required: true})
  available: boolean;

  @property({type: 'array', itemType: 'string', required: true})
  suggestions: string[];
  constructor(data?: Partial<KeySuggestionDto>) {
    super(data);
  }
}
