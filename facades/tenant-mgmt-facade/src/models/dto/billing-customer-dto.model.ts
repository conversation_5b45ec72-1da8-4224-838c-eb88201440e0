import {model, property} from '@loopback/repository';
import {CustomerDto as BillingCustomerDto} from '@sourceloop/ctrl-plane-subscription-service';

/**
 * Data transfer object (DTO) representing a customer
 * in the context of a tenant.
 *
 * Extends the base BillingCustomerDto from the subscription service.
 */
@model()
export class CustomerDto extends BillingCustomerDto {
  /**
   * Unique identifier of the tenant associated with the customer.
   */
  @property({
    type: 'string',
    required: true,
  })
  tenantId: string;

  /**
   * Optional display name of the customer.
   */
  @property({
    type: 'string',
  })
  name?: string;

  /**
   * Creates an instance of CustomerDto.
   *
   * @param data - Partial data to initialize the DTO
   */
  constructor(data?: Partial<CustomerDto>) {
    super(data);
  }
}
