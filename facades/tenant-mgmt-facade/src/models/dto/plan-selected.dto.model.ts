import {model, property} from '@loopback/repository';

@model()
export class PlanSelectedDto {
  @property({
    type: 'string',
    required: false,
  })
  planId: string;

  @property({
    type: 'string',
    required: false,
  })
  name?: string;

  @property({
    type: 'string',
    required: false,
  })
  duration?: string;

  @property({
    type: 'string', // or 'number' if you're always expecting a numeric amount
    required: false,
  })
  amount?: string | number;

  @property({
    type: 'string',
    required: false,
  })
  billingCycleId?: string;
}
