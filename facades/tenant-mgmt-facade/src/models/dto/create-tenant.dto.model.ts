import {getJsonSchema} from '@loopback/openapi-v3';
import {Entity, model, property} from '@loopback/repository';
import {fileProperty} from '@sourceloop/file-utils';
import {MulterS3Storage} from '@sourceloop/file-utils/s3';
import {Contact} from '../contact.model';
import {ALLOWED_FILE_EXTENSIONS} from '../../constant';
@model({
  description: 'model describing payload used to create and onboard a tenant',
  settings: {multer: {limitsProvider: true}},
})
export class CreateTenantDTO extends Entity {
  @property({
    type: 'object',
    description:
      'metadata for the contact to be created, it is required when tenant is created without a lead',
    jsonSchema: getJsonSchema(Contact, {
      exclude: ['tenantId', 'id'],
    }),
  })
  contact: Omit<Contact, 'id' | 'tenantId'>;

  @property({
    type: 'string',
    name: 'lang',
    required: true,
    jsonSchema: {
      enum: ['English'], // Only allow 'english' as a valid value
    },
  })
  lang: string;

  @property({
    type: 'string',
    description: 'address of the tenant owners',
  })
  address?: string;

  @property({
    type: 'number',
    description: 'number of users for the tenant',
  })
  numberOfUsers?: number;

  @property({
    type: 'string',
    description: 'city of the tenant owner',
  })
  city?: string;

  @property({
    description: 'state of the tenant owner',
    type: 'string',
  })
  state?: string;

  @property({
    description: 'zip code of the tenant owner',
    type: 'string',
  })
  zip?: string;

  @property({
    type: 'string',
    description: 'country of the tenant owner',
  })
  country?: string;

  @property({
    type: 'number',
    description: 'total cost of the tenant',
    required: true,
  })
  totalCost: number;

  @property({
    type: 'string',
    name: 'name',
    required: true,
    jsonSchema: {
      minLength: 3,
      maxLength: 50,
      pattern: "^(?![-\\s])[a-zA-Z0-9\\s&.,'’-]+(?<![-\\s])$",
      errorMessage: {
        pattern:
          "Invalid name format. Use only letters, numbers, spaces, &, ., ,, ', and –. It cannot start or end with a space or hyphen.",
      },
    },
  })
  name: string;

  @property({
    type: 'string',
    required: true,
  })
  planId: string;

  @fileProperty({
    type: 'array',
    itemType: 'string',
    extensions: ALLOWED_FILE_EXTENSIONS,
    storageOptions: {
      storageClass: MulterS3Storage,
      options: {
        bucket: process.env.AWS_S3_BUCKET,
      },
    },
  })
  files: Express.Multer.File[];

  @property({
    type: 'string',
    description:
      'A short string used to identify a tenant. This is also used as the namespace and subdomain for this particular tenant.',
    required: true,
    jsonSchema: {
      pattern: '^(?!-)[a-zA-Z0-9-]{3,63}(?<!-)$',
      minLength: 3,
      maxLength: 63,
    },
  })
  key: string;

  constructor(data?: Partial<CreateTenantDTO>) {
    super(data);
  }
}
