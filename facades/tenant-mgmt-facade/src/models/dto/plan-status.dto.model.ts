import {PlanStatus} from '@local/core';
import {model, Model, property} from '@loopback/repository';
@model({
  description: 'model describing payload used to verify key for a tenant',
})
export class PlanStatusDto extends Model {
  @property({
    type: 'number',
    description: 'Status of the plan.',
    required: true,
    jsonSchema: {
      enum: Object.values(PlanStatus).filter(v => typeof v === 'number'),
    },
  })
  status: PlanStatus;
  constructor(data?: Partial<PlanStatusDto>) {
    super(data);
  }
}
