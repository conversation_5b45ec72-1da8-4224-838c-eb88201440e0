import {model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';

/**
 * ConfigureDevice model representing device configuration details.
 */
@model({
  name: 'configure_devices',
})
export class ConfigureDevice extends UserModifiableEntity {
  /**
   * Unique identifier for the ConfigureDevice entity.
   */
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id: string;

  /**
   * Minimum configuration value.
   */
  @property({
    type: 'number',
    required: true,
  })
  min: number;

  /**
   * Maximum configuration value.
   */
  @property({
    type: 'number',
    required: true,
  })
  max: number;

  /**
   * compute size value.
   */
  @property({
    type: 'string',
    name: 'compute_size',
  })
  computeSize?: string;

  /**
   * db size value.
   */
  @property({
    type: 'string',
    name: 'db_size',
  })
  dbSize?: string;

  /**
   * Creates a new instance of ConfigureDevice.
   * @param data - Partial data to initialize the model.
   */
  constructor(data?: Partial<ConfigureDevice>) {
    super(data);
  }
}
