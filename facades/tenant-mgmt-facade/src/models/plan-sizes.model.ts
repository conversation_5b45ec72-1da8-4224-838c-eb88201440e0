import {model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';

/**
 * PlanSizes model representing sizes of plans.
 */
@model({
  name: 'plan_sizes',
})
export class PlanSizes extends UserModifiableEntity {
  /**
   * Unique identifier for the PlanSizes entity.
   */
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id: string;

  /**
   * Size designation (e.g., small, medium, large).
   */
  @property({
    type: 'string',
    required: true,
  })
  size: string;

  /**
   * Optional configuration object related to the size.
   */
  @property({
    type: 'object',
  })
  config?: object;

  /**
   * Creates a new instance of PlanSizes.
   * @param data - Partial data to initialize the model.
   */
  constructor(data?: Partial<PlanSizes>) {
    super(data);
  }
}

/**
 * Interface describing relations of PlanSizes (currently empty).
 */
export interface PlanSizesRelations {}

/**
 * Type combining PlanSizes with its relations.
 */
export type PlanSizesWithRelations = PlanSizes;
