import {model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';

/**
 * BillingCycle model representing billing cycle details.
 */
@model({
  name: 'billing_cycles',
})
export class BillingCycle extends UserModifiableEntity {
  /**
   * Unique identifier for the billing cycle.
   */
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id: string;

  /**
   * Name of the billing cycle.
   */
  @property({
    type: 'string',
    required: true,
    name: 'cycle_name',
  })
  cycleName: string;

  /**
   * Duration of the billing cycle.
   */
  @property({
    type: 'number',
    required: true,
  })
  duration: number;

  /**
   * Unit of the duration (e.g., days, months).
   */
  @property({
    type: 'string',
    required: true,
    name: 'duration_unit',
  })
  durationUnit: string;

  /**
   * Optional description about the billing cycle.
   */
  @property({
    type: 'string',
  })
  description?: string;

  /**
   * Creates a new instance of BillingCycle.
   * @param data - Partial data to initialize the model.
   */
  constructor(data?: Partial<BillingCycle>) {
    super(data);
  }
}
