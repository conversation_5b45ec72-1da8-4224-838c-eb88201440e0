import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {Plan} from './plan.model';
import {SubscriptionStatus} from '@sourceloop/ctrl-plane-subscription-service';
import {numericEnumValues} from '@local/core';

@model({
  name: 'subscriptions',
})
export class Subscription extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id: string;

  @property({
    type: 'string',
    required: true,
    name: 'subscriber_id',
  })
  subscriberId: string;

  @property({
    type: 'string',
    required: true,
    name: 'external_subscription_id',
  })
  externalSubscriptionId: string;

  @property({
    type: 'string',
    description: 'price reference ID of the subscription.',
    required: true,
    name: 'price_ref_id',
  })
  priceRefId: string;

  @property({
    type: 'string',
    name: 'start_date',
  })
  startDate?: string;

  @property({
    type: 'string',
    name: 'end_date',
  })
  endDate?: string;

  @property({
    type: 'number',
    description: 'number of users for the tenant',
    name: 'number_of_users',
  })
  numberOfUsers?: number;

  @property({
    type: 'number',
    name: 'total_cost',
    description: 'total cost of the tenant',
    required: true,
  })
  totalCost: number;

  @property({
    type: 'number',
    required: true,
    description:
      'status of the subscription, it can be - 0(pending), 1(active), 2(inactive), 3(cancelled) and 4(expired)',
    jsonSchema: {
      enum: numericEnumValues(SubscriptionStatus),
    },
  })
  status: SubscriptionStatus;

  @belongsTo(() => Plan, undefined, {
    description: 'plan id of the subscription',
    name: 'plan_id',
  })
  planId: string;

  constructor(data?: Partial<Subscription>) {
    super(data);
  }
}

export interface SubscriptionRelations {
  plan: Plan;
}

export type SubscriptionWithRelations = Subscription & SubscriptionRelations;
