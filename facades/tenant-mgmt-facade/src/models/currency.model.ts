import {Entity, model, property} from '@loopback/repository';

/**
 * Currency model representing currency details.
 */
@model({
  name: 'currencies',
})
export class Currency extends Entity {
  /**
   * Unique identifier for the Currency.
   */
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id: string;

  /**
   * Currency code (e.g., USD, EUR).
   */
  @property({
    type: 'string',
    required: true,
    name: 'currency_code',
  })
  currencyCode: string;

  /**
   * Full name of the currency.
   */
  @property({
    type: 'string',
    required: true,
    name: 'currency_name',
  })
  currencyName: string;

  /**
   * Symbol used for the currency (e.g., $, €).
   */
  @property({
    type: 'string',
    name: 'symbol',
  })
  symbol?: string;

  /**
   * Country associated with the currency.
   */
  @property({
    type: 'string',
    name: 'country',
  })
  country?: string;

  /**
   * Creates a new instance of Currency.
   * @param data - Partial data to initialize the model.
   */
  constructor(data?: Partial<Currency>) {
    super(data);
  }
}
