import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {Plan} from './plan.model';

/**
 * PlanHistory model representing historical versions of plans.
 */
@model({
  name: 'plan_history',
})
export class PlanHistory extends UserModifiableEntity {
  /**
   * Unique identifier for the PlanHistory record.
   */
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id: string;

  /**
   * Price associated with this plan version.
   */
  @property({
    type: 'number',
    precision: 10,
    scale: 2,
  })
  price?: number;

  /**
   * Version string for the plan history entry.
   */
  @property({
    type: 'string',
    required: true,
  })
  version: string;

  /**
   * Indicates if the plan allows unlimited users.
   */
  @property({
    type: 'boolean',
    description: 'Indicates if the plan allows unlimited users.',
    name: 'allowed_unlimited_users',
  })
  allowedUnlimitedUsers: boolean;

  /**
   * Price added per user for the plan, if applicable.
   */
  @property({
    type: 'number',
    description: 'price added per user for the plan.',
    name: 'cost_per_user',
  })
  costPerUser?: number;

  /**
   * Foreign key referencing the associated Plan.
   */
  @belongsTo(
    () => Plan,
    {
      keyTo: 'id',
    },
    {
      name: 'plan_id',
    },
  )
  planId: string;

  /**
   * Creates a new instance of PlanHistory.
   * @param data - Partial data to initialize the model.
   */
  constructor(data?: Partial<PlanHistory>) {
    super(data);
  }
}
