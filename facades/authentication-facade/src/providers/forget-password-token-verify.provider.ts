import {Provider, ValueOrPromise} from '@loopback/core';
import {verify} from 'jsonwebtoken';
import {VerifyFunction} from 'loopback4-authentication';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {User} from '@sourceloop/authentication-service';
import {UserTokenRepository} from '../repositories/user-token.repository';

/**
 * Provider that implements the Bearer token verification function
 * for the forget password flow.
 */
export class ForgetPasswordTokenVerifierProvider
  implements Provider<VerifyFunction.BearerFn<User>>
{
  /**
   * Creates a new instance of ForgetPasswordTokenVerifierProvider.
   *
   * @param userTokenRepository - Repository to access user tokens stored in the system.
   */
  constructor(
    @repository(UserTokenRepository)
    public userTokenRepository: UserTokenRepository,
  ) {}

  /**
   * Returns a function that verifies a Bearer token and returns the associated user.
   *
   * @returns A function that takes a token, validates it, and returns a User object.
   * @throws HttpErrors.Unauthorized - If the token is not found or invalid.
   * @throws Error - If `JWT_SECRET` is not set in environment variables.
   */
  value(): ValueOrPromise<VerifyFunction.BearerFn<User>> {
    return async token => {
      const response = await this.userTokenRepository.get(token);

      if (!response.token) {
        throw new HttpErrors.Unauthorized();
      }

      const secret = process.env.JWT_SECRET;
      if (!secret) {
        throw new Error(
          'JWT_SECRET is not defined in the environment variables.',
        );
      }

      // Verify the JWT token using the secret and issuer
      // sonarignore:start
      const data = verify(response.token, secret, {
        // sonarignore:end
        issuer: process.env.JWT_ISSUER,
        algorithms: ['HS256'],
      }) as Object;

      return data as User;
    };
  }
}
