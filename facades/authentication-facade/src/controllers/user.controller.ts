import {PermissionKey} from '@local/core';
import {inject} from '@loopback/context';
import {Count, Filter, Where} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  param,
  RestBindings,
  Request,
} from '@loopback/rest';
import {OPERATION_SECURITY_SPEC, STATUS_CODE} from '@sourceloop/core';
import {User, UserView} from '@sourceloop/user-tenant-service';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {UserHelperService, UserTenantProxyService} from '../services';

const baseUrl = '/users';
/**
 * Controller for handling user-related endpoints.
 * Provides APIs to fetch user lists and user counts for tenants.
 */
export class UserController {
  /**
   * Constructs a new UserController.
   * @param userOperationsService - Service for user operations via proxy.
   * @param userHelperService - Helper service for user-related logic.
   * @param request - The current HTTP request object.
   */
  constructor(
    @inject('services.UserTenantProxyService')
    private readonly userOperationsService: UserTenantProxyService,
    @inject('services.UserHelperService')
    private readonly userHelperService: UserHelperService,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {}

  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @authorize({
    permissions: [PermissionKey.ViewTenantUser],
  })
  @get(baseUrl, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Users of Tenant',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(User)},
          },
        },
      },
    },
  })
  /**
   * Retrieves a list of users
   * Requires bearer authentication and appropriate permissions.
   * @param filter - Optional filter criteria for querying users.
   * @returns A promise resolving to an array of UserView objects.
   */
  async find(
    @param.query.object('filter') filter?: Filter<UserView>,
  ): Promise<UserView[]> {
    // Extract the bearer token from the Authorization header
    const token = (this.request.headers.authorization ?? '')
      .trim()
      .replace(/^Bearer\s+/i, '')
      .trim();
    return this.userOperationsService.find(
      token,
      process.env.DEFAULT_TENANT_ID ?? '',
      filter,
    );
  }

  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @authorize({
    permissions: [PermissionKey.ViewTenantUser],
  })
  @get(`${baseUrl}/user-list`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Users',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(User)},
          },
        },
      },
    },
  })
  /**
   * Retrieves a refined list of users, excluding super admin users.
   * Requires bearer authentication and appropriate permissions.
   * @param filter - Optional filter criteria for querying users.
   * @returns A promise resolving to an array of UserView objects.
   */
  async getUserLists(
    @param.filter(UserView) filter?: Filter<UserView>,
  ): Promise<UserView[]> {
    // Delegate the user list retrieval to the userHelperService
    return this.userHelperService.getUserLists(filter);
  }

  @authorize({
    permissions: [PermissionKey.ViewTenantUser],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${baseUrl}/count`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'count of Users',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                count: {type: 'number'},
              },
            },
          },
        },
      },
    },
  })
  /**
   * Retrieves the count of users, excluding super admin users.
   * Requires bearer authentication and appropriate permissions.
   * @param where - Optional filter criteria for users.
   * @returns A promise resolving to the count of users.
   */
  async getUserCount(
    @param.where(UserView)
    where?: Where<UserView>,
  ): Promise<Count> {
    // Delegate the user count retrieval to the userHelperService
    return this.userHelperService.getUsersCount(where);
  }
}
