import {inject} from '@loopback/core';
import {
  get,
  post,
  requestBody,
  RestBind<PERSON>,
  Request,
  param,
} from '@loopback/rest';
import {
  AuthRefreshTokenRequest,
  AuthUser,
  RefreshTokenRequest,
  TokenResponse,
} from '@sourceloop/authentication-service';
import {
  CONTENT_TYPE,
  ErrorCodes,
  IAuthUserWithPermissions,
  OPERATION_SECURITY_SPEC,
  STATUS_CODE,
  X_TS_TYPE,
} from '@sourceloop/core';
import {
  authenticate,
  AuthenticationBindings,
  STRATEGY,
} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {LoginHelperService} from '../services';
import {AuthTokenRequest, LoginRequest} from '../models';
/**SUPER ADMIN PORTAL LOGIN CONTROLLER */
/**
 * Controller responsible for handling authentication-related endpoints,
 * including login and token generation.
 *
 * @remarks
 * This controller provides endpoints for:
 * - Sending OTP to user email and returning an auth code (`/auth/login`)
 * - Exchanging an OTP code for access and refresh tokens (`/auth/token`)
 *
 * @constructor
 * @param currentUser - The currently authenticated user with permissions.
 * @param loginHelperService - Service handling the login and token logic.
 *
 * @method login
 * Handles POST `/auth/login`. Sends an OTP to the user's email and returns an auth code.
 *
 * @method getToken
 * Handles POST `/auth/token`. Exchanges the OTP code for access and refresh tokens.
 */
export class LoginController {
  /**
   * Constructor for LoginController.
   *
   * @param currentUser - The currently authenticated user, injected from authentication context.
   * @param loginHelperService - The service that encapsulates login and authentication logic.
   */
  constructor(
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @inject(AuthenticationBindings.CURRENT_USER)
    private readonly currentUser: IAuthUserWithPermissions,
    @inject('services.LoginHelperService')
    private readonly loginHelperService: LoginHelperService,
  ) {}

  /**
   * Initiates login by triggering OTP (One Time Password) to the user's email.
   *
   * @remarks
   * This endpoint sends an OTP to the email address associated with the login request.
   * Use the `/auth/verify-otp` endpoint to validate the OTP.
   *
   * @param req - Login request payload containing email or credentials.
   * @returns A void promise if the OTP is successfully sent.
   *
   * @response 200 - OTP sent successfully.
   * @response 4xx/5xx - Error messages from the `ErrorCodes` enum.
   */
  @authorize({permissions: ['*']})
  @post('/auth/login', {
    description:
      'Gets you the otp success message that that means the otp has been sent to user email succesfully',
    responses: {
      [STATUS_CODE.OK]: {
        description: `'Auth Code that you can use to generate access
          and refresh tokens using the POST /auth/token API'`,
        content: {
          [CONTENT_TYPE.JSON]: Object,
        },
      },
      ...ErrorCodes,
    },
  })
  async login(
    @requestBody()
    req: LoginRequest,
  ): Promise<TokenResponse> {
    return this.loginHelperService.login(req);
  }

  /**
   * Exchanges the authorization code for an access token and a refresh token.
   *
   * @remarks
   * The authorization code is received after a successful OTP verification.
   * This endpoint generates and returns access and refresh tokens for authentication.
   *
   * @param req - Payload containing the authorization code and other metadata.
   * @returns A `TokenResponse` containing the tokens.
   *
   * @response 200 - Tokens generated successfully.
   */
  @authorize({permissions: ['*']})
  @post('/auth/token', {
    description: `'Send the code received from the OTP verify api
        and get refresh token and access token (webapps)'`,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Token Response',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {[X_TS_TYPE]: TokenResponse},
          },
        },
      },
      ...ErrorCodes,
    },
  })
  async getToken(@requestBody() req: AuthTokenRequest): Promise<TokenResponse> {
    return this.loginHelperService.getToken(req);
  }
  /**
   * Retrieves details of the currently authenticated user.
   *
   * @remarks
   * Requires a valid bearer token in the request header.
   * Returns basic user profile data from the authentication service.
   *
   * @returns The authenticated user's profile or `undefined`.
   *
   * @response 200 - Returns the user object.
   */
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @authorize({permissions: ['*']})
  @get('/auth/me', {
    security: OPERATION_SECURITY_SPEC,
    description: 'To get the user details',
    responses: {
      [STATUS_CODE.OK]: {
        description: 'User Object',
        content: {
          [CONTENT_TYPE.JSON]: AuthUser,
        },
      },
      ...ErrorCodes,
    },
  })
  async me(): Promise<AuthUser | undefined> {
    return this.loginHelperService.me();
  }

  /**
   * Logs out the user by invalidating the access and refresh tokens.
   *
   * @param refreshTokenReqBody - The request body containing the refresh token to be invalidated.
   * @param authorization - The Bearer token from the request header used to authenticate the user.
   * @returns A promise that resolves to an object indicating the logout status.
   *
   * @remarks
   * This endpoint requires Bearer authentication and will invalidate both the access and refresh tokens.
   *
   * @route POST /auth/logout
   * @security BearerAuth
   */
  @authorize({permissions: ['*']})
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @post('/auth/logout', {
    security: OPERATION_SECURITY_SPEC,
    description:
      'Logs out the user by invalidating the access and refresh tokens',
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Logout successful',
      },
      ...ErrorCodes,
    },
  })
  async logout(
    @requestBody() refreshTokenReqBody: RefreshTokenRequest,
    @param.header.string('authorization') authorization: string,
  ): Promise<object> {
    const result = await this.loginHelperService.logout(
      authorization,
      refreshTokenReqBody,
    );
    return result;
  }

  @authorize({permissions: ['*']})
  @post('/auth/token-refresh', {
    security: OPERATION_SECURITY_SPEC,
    description:
      'Gets you a new access and refresh token once your access token is expired',
    //(both mobile and web)
    responses: {
      [STATUS_CODE.OK]: {
        description: 'New Token Response',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {[X_TS_TYPE]: TokenResponse},
          },
        },
      },
      ...ErrorCodes,
    },
  })
  /**
   * Exchanges a refresh token for a new access token.
   *
   * @param req - The request body containing the refresh token and other required fields, excluding 'tenantId'.
   * @returns A promise that resolves to a new token response.
   * @throws Error if the 'Authorization' header is missing in the request.
   */
  async exchangeToken(
    @requestBody() req: Omit<AuthRefreshTokenRequest, 'tenantId'>,
  ): Promise<TokenResponse> {
    const authorization = this.request.headers.authorization;
    if (!authorization) {
      throw new Error('Authorization header is missing');
    }
    return this.loginHelperService.refreshToken(authorization, req);
  }
}
