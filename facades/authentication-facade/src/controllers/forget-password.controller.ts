﻿// Copyright (c) 2023 Sourcefuse Technologies
//
// This software is released under the MIT License.
// https://opensource.org/licenses/MIT

import {inject} from '@loopback/core';
import {getModelSchemaRef, post, requestBody} from '@loopback/rest';
import {
  CONTENT_TYPE,
  <PERSON><PERSON>r<PERSON><PERSON>,
  OPERATION_SECURITY_SPEC,
  STATUS_CODE,
} from '@sourceloop/core';
import {STRATEGY, authenticate} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {FORGET_PASSWORD_TOKEN_VERIFIER} from '../key';
import {UpdatePasswordDto} from '../models/dto';
import {ForgetPasswordRequestDto} from '../models/dto/forget-password-req.dto.model';
import {ForgetPasswordHelperService} from '../services/forget-password-helper.service';

/**
 * Controller for handling forget and update password related endpoints.
 */
export class ForgetPasswordController {
  /**
   * Creates a new instance of the ForgetPasswordController.
   *
   * @param forgetPasswordHelperService - Service to handle forget password logic.
   */
  constructor(
    @inject('services.ForgetPasswordHelperService')
    private readonly forgetPasswordHelperService: ForgetPasswordHelperService,
  ) {}

  /**
   * Endpoint to initiate the forget password process.
   *
   * @param dto - Data Transfer Object containing email or other identifier to initiate password reset.
   * @returns A void promise indicating completion of the request.
   *
   * @remarks
   * This endpoint triggers an email or notification with a reset token to the user.
   *
   * @endpoint POST /auth/forget-password
   * @status 204 No Content on success
   * @security Authorization required with any permission.
   */
  @authorize({permissions: ['*']})
  @post(`auth/forget-password`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Success Response.',
      },
      ...ErrorCodes,
    },
  })
  async forgetPassword(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ForgetPasswordRequestDto),
        },
      },
    })
    dto: ForgetPasswordRequestDto,
  ): Promise<void> {
    await this.forgetPasswordHelperService.forgetPassword(dto);
  }

  /**
   * Endpoint to update the password after verifying the token.
   *
   * @param dto - Data Transfer Object containing new password and token.
   * @returns A void promise indicating completion of the password update.
   *
   * @remarks
   * This endpoint is protected using Bearer token authentication and a custom token verifier.
   *
   * @endpoint POST /auth/forget-password/verify
   * @status 204 No Content on success
   * @security Bearer token authentication with forget password token verifier.
   */
  @authorize({permissions: ['*']})
  @authenticate(
    STRATEGY.BEARER,
    {
      passReqToCallback: true,
    },
    undefined,
    FORGET_PASSWORD_TOKEN_VERIFIER,
  )
  @post(`auth/forget-password/verify`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Success Response.',
      },
      ...ErrorCodes,
    },
  })
  async updatePassword(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(UpdatePasswordDto),
        },
      },
    })
    dto: UpdatePasswordDto,
  ): Promise<void> {
    await this.forgetPasswordHelperService.updatePassword(dto);
  }
}
