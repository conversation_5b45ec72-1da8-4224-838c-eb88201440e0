import {AuthenticationFacadeApplication} from '../..';
import {
  createRestAppClient,
  givenHttpServerConfig,
  Client,
} from '@loopback/testlab';
import {
  User,
  UserCredentials,
  AuthCacheSourceName,
} from '@sourceloop/authentication-service';
import {sign} from 'jsonwebtoken';
import {RateLimitSecurityBindings} from 'loopback4-ratelimiter';

export async function setupApplication(): Promise<AppWithClient> {
  const restConfig = givenHttpServerConfig({
    // Customize the server configuration here.
    // Empty values (undefined, '') will be ignored by the helper.
    //
    // host: process.env.HOST,
    // port: +process.env.PORT,
  });
  setUpEnv();

  const app = new AuthenticationFacadeApplication({
    rest: restConfig,
  });

  app.bind(`datasources.config.${AuthCacheSourceName}`).to({
    name: AuthCacheSourceName,
    connector: 'kv-memory',
  });

  app.bind(RateLimitSecurityBindings.RATELIMIT_SECURITY_ACTION).to(async () => {
    /* nothing here */
  });

  await app.boot();
  await app.start();

  const client = createRestAppClient(app);

  return {app, client};
}

function setUpEnv() {
  process.env.NODE_ENV = 'test';
  process.env.ENABLE_TRACING = '0';
  process.env.ENABLE_OBF = '0';
  process.env.REDIS_NAME = 'redis';
  process.env.HOST = 'localhost';
  process.env.PORT = '3000';
  process.env.AUTHENTICATION_SERVICE_URL = 'http://localhost:3000';
  process.env.CLIENT_ID = 'test-client';
  process.env.CLIENT_SECRET = 'test-secret';
  process.env.JWT_ISSUER = 'test';
  process.env.JWT_SECRET = '6356345364';
  process.env.DEFAULT_TENANT_ID = 'default-tenant';
  process.env.FORGET_PASSWORD_KEY_LENGTH = '4';
  process.env.FORGET_PASSWORD_LINK_EXPIRY = '30000000';
}

export const mockUser: User = new User({
  id: '123',
  firstName: 'John',
  lastName: 'Doe',
  username: 'johndoe',
  email: '<EMAIL>',
  dob: new Date('1990-01-01'),
  credentials: [] as unknown as UserCredentials,
  defaultTenantId: 'tenant-1',
  userTenants: [],
  phone: '',
  gender: undefined,
  createdOn: new Date(),
  lastLogin: new Date(),
});
export function getToken(permissions: string[] = [], withoutBearer = false) {
  return `${withoutBearer ? '' : 'Bearer '}${sign(
    {
      id: 'test',
      userTenantId: 'test',
      iss: process.env.JWT_ISSUER,
      permissions,
    },
    process.env.JWT_SECRET ?? '',
  )}`;
}

export interface AppWithClient {
  app: AuthenticationFacadeApplication;
  client: Client;
}
