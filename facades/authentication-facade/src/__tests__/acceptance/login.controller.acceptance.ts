import {Client, expect, sinon} from '@loopback/testlab';
import {setupApplication} from './test-helper';
import {LoginHelperService} from '../../services';
import {TokenResponse} from '@sourceloop/authentication-service';
import {AuthenticationFacadeApplication} from '../../application';

describe('LoginController Acceptance', () => {
  let app: AuthenticationFacadeApplication;
  let client: Client;
  let loginHelperServiceStub: Partial<LoginHelperService>;

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());

    // Create a stubbed version of LoginHelperService
    loginHelperServiceStub = {
      login: sinon.stub().resolves(),
      getToken: sinon.stub().resolves({
        accessToken: 'fake-access-token',
        refreshToken: 'fake-refresh-token',
        expiresIn: 3600,
      } as unknown as TokenResponse),
      refreshToken: sinon.stub().resolves({
        accessToken: 'fake-access-token',
        refreshToken: 'fake-refresh-token',
        expiresIn: 3600,
      } as unknown as TokenResponse),
    };

    // Bind stubbed service to override the real one
    app.bind('services.LoginHelperService').to(loginHelperServiceStub);
  });

  after(async () => {
    await app.stop();
  });

  it('POST /auth/login - should call login service and return 204', async () => {
    const payload = {
      username: '<EMAIL>',
      password: 'test123',
    };

    await client.post('/auth/login').send(payload).expect(204);

    sinon.assert.calledOnce(loginHelperServiceStub.login as sinon.SinonStub);
  });

  it('POST /auth/token - should return token response', async () => {
    const payload = {
      code: 'TEST_CODE',
    };

    const res = await client.post('/auth/token').send(payload).expect(200);

    expect(res.body).to.containEql({
      accessToken: 'fake-access-token',
      refreshToken: 'fake-refresh-token',
    });

    sinon.assert.calledOnce(loginHelperServiceStub.getToken as sinon.SinonStub);
  });

  it('POST /auth/token-refresh - should return token response', async () => {
    const payload = {
      refreshToken: 'TEST_CODE',
    };

    const res = await client
      .post('/auth/token-refresh')
      .set('Authorization', 'Bearer fake-access-token')
      .send(payload)
      .expect(200);

    expect(res.body).to.containEql({
      accessToken: 'fake-access-token',
      refreshToken: 'fake-refresh-token',
    });

    sinon.assert.calledOnce(
      loginHelperServiceStub.refreshToken as sinon.SinonStub,
    );
  });
});
