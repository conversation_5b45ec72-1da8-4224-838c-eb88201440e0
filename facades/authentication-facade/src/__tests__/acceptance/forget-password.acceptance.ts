import {NotificationTemplates, PermissionKey} from '@local/core';
import {Client, expect} from '@loopback/testlab';
import {User} from '@sourceloop/authentication-service';
import sinon, {SinonStubbedInstance} from 'sinon';
import {AuthenticationFacadeApplication} from '../../application';
import {UserToken} from '../../models';
import {ForgetPasswordRequestDto, UpdatePasswordDto} from '../../models/dto';
import {UserTokenRepository} from '../../repositories/user-token.repository';
import {
  AuthenticationProxyService,
  NotificationProxyService,
  UserTenantProxyService,
} from '../../services';
import {getToken, mockUser, setupApplication} from './test-helper';
// Import the class, not just the type
describe('ForgetPasswordController (acceptance)', () => {
  let app: AuthenticationFacadeApplication;
  let client: Client;
  let userTenantProxyStub: SinonStubbedInstance<UserTenantProxyService>;
  let userTokenRepositoryStub: SinonStubbedInstance<UserTokenRepository>;
  let notificationProxyServiceStub: SinonStubbedInstance<NotificationProxyService>;
  let authenticationProxyServiceStub: SinonStubbedInstance<AuthenticationProxyService>;
  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
  });

  beforeEach(() => {
    userTenantProxyStub = {
      find: sinon
        .stub<
          [
            filter?: import('@loopback/repository').Filter<User>,
            options?: import('@loopback/repository').Options,
          ],
          Promise<User[]>
        >()
        .resolves([mockUser]),
    } as unknown as sinon.SinonStubbedInstance<UserTenantProxyService>;
    userTokenRepositoryStub = {
      set: sinon.stub().resolves(),
      expire: sinon.stub().resolves(),
      delete: sinon.stub().resolves(),
      get: sinon.stub(),
    } as unknown as sinon.SinonStubbedInstance<UserTokenRepository>;
    notificationProxyServiceStub = {
      createNotification: sinon.stub().resolves(),
      getTemplateByName: sinon.stub().resolves({
        subject: 'Test Subject',
        body: 'Test Body',
      } as unknown as NotificationTemplates),
    } as unknown as sinon.SinonStubbedInstance<NotificationProxyService>;
    authenticationProxyServiceStub = {
      updatePassword: sinon.stub().resolves(),
    } as unknown as sinon.SinonStubbedInstance<AuthenticationProxyService>;
    app.bind('services.UserTenantProxyService').to(userTenantProxyStub);

    app.bind('repositories.UserTokenRepository').to(userTokenRepositoryStub);
    app
      .bind('services.NotificationProxyService')
      .to(notificationProxyServiceStub);
    app
      .bind('services.AuthenticationProxyService')
      .to(authenticationProxyServiceStub);
  });
  after(async () => {
    await app.stop();
  });
  afterEach(async () => {
    sinon.restore();

    app.unbind('services.UserTenantProxyService');
    app.unbind('repositories.UserTokenRepository');
    app.unbind('services.NotificationProxyService');
    app.unbind('services.AuthenticationProxyService');
  });

  describe('POST /auth/forget-password', () => {
    it('invokes forget-password endpoint with valid email', async () => {
      const requestDto: ForgetPasswordRequestDto = new ForgetPasswordRequestDto(
        {
          email: '<EMAIL>',
        },
      );

      const res = await client
        .post('/auth/forget-password')
        .send(requestDto)
        .expect(204);

      expect(res.body).to.be.empty();
    });

    it('returns 422 if email is missing', async () => {
      await client.post('/auth/forget-password').send({}).expect(422);
    });
  });

  describe('POST /auth/forget-password/verify', () => {
    it('invokes with valid token and new password', async () => {
      const token = getToken([PermissionKey.UpdatePassword]);
      const rawToken = token.replace(/^Bearer\s+/i, '');

      userTokenRepositoryStub.get.resolves(
        new UserToken({
          token: rawToken,
        }),
      );
      const updateDto: UpdatePasswordDto = new UpdatePasswordDto({
        newPassword: 'NewP@ssword123',
      });

      const res = await client
        .post('/auth/forget-password/verify')
        .set('Authorization', token)
        .send(updateDto)
        .expect(204);

      expect(res.body).to.be.empty();
    });

    it('returns 422 if required fields are missing', async () => {
      const token = getToken([PermissionKey.UpdatePassword]);
      const rawToken = token.replace(/^Bearer\s+/i, '');

      userTokenRepositoryStub.get.resolves(
        new UserToken({
          token: rawToken,
        }),
      );
      await client
        .post('/auth/forget-password/verify')
        .set('Authorization', token)
        .send({
          newPassword: undefined,
        })
        .expect(422);
    });

    it('returns 403 if token is missing', async () => {
      const updateDto: UpdatePasswordDto = new UpdatePasswordDto({
        newPassword: 'NewP@ssword123',
      });

      await client
        .post('/auth/forget-password/verify')
        .send(updateDto)
        .expect(401);
    });
  });
});
