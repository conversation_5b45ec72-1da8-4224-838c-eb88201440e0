import {Client, expect, sinon} from '@loopback/testlab';
import {getToken, setupApplication} from './test-helper';
import {AuthenticationFacadeApplication} from '../../application';
import {UserTenantProxyService} from '../../services';
import {UserView} from '@sourceloop/user-tenant-service';
import {PermissionKey} from '@local/core';

describe('UserController Acceptance', () => {
  let app: AuthenticationFacadeApplication;
  let client: Client;
  let userTenantProxyServiceStub: Partial<UserTenantProxyService>;

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());

    // Stub service
    userTenantProxyServiceStub = {
      find: sinon.stub().resolves([
        {
          id: 'user-1',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
        } as unknown as UserView,
      ]),
    };

    // Bind stub to app
    app.bind('services.UserTenantProxyService').to(userTenantProxyServiceStub);
  });

  after(async () => {
    await app.stop();
  });

  it('GET /users - should call UserTenantProxyService.find and return users', async () => {
    const fakeToken = getToken([PermissionKey.ViewTenantUser], false);
    const fakeTenantId = process.env.DEFAULT_TENANT_ID ?? '';

    const res = await client
      .get('/users')
      .set('Authorization', fakeToken)
      .expect(200);

    expect(res.body).to.deepEqual([
      {
        id: 'user-1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
      },
    ]);

    sinon.assert.calledOnce(userTenantProxyServiceStub.find as sinon.SinonStub);

    const [passedToken, passedTenantId, passedFilter] = (
      userTenantProxyServiceStub.find as sinon.SinonStub
    ).getCall(0).args;

    // Ensure Bearer is stripped
    expect(passedToken).to.equal(fakeToken.replace(/^bearer\s+/i, ''));
    expect(passedTenantId).to.equal(fakeTenantId);
    expect(passedFilter).to.equal(undefined);
  });

  it('GET /users with filter - should pass filter to service', async () => {
    const fakeToken = getToken([PermissionKey.ViewTenantUser], false);
    const filter = {where: {firstName: 'John'}};

    await client
      .get('/users')
      .query({filter: JSON.stringify(filter)})
      .set('Authorization', fakeToken)
      .expect(200);

    const [, , passedFilter] = (
      userTenantProxyServiceStub.find as sinon.SinonStub
    ).getCall(1).args;

    expect(passedFilter).to.deepEqual(filter);
  });
});

// Acceptance tests for /users/userLists (getUserLists)
describe('UserController getUserLists Acceptance', () => {
  let app: AuthenticationFacadeApplication;
  let client: Client;
  let userHelperServiceStub: {getUserLists: sinon.SinonStub};

  before('setupApplication', async () => {
    const setup = await setupApplication();
    app = setup.app;
    client = setup.client;

    userHelperServiceStub = {
      getUserLists: sinon.stub().resolves([
        {
          id: 'user-2',
          firstName: 'Alice',
          lastName: 'Smith',
          email: '<EMAIL>',
        },
      ]),
    };

    app.bind('services.UserHelperService').to(userHelperServiceStub);
  });

  after(async () => {
    await app.stop();
  });

  it('GET /users/userLists returns user list from helper', async () => {
    const fakeToken = getToken([PermissionKey.ViewTenantUser], false);
    const res = await client
      .get('/users/user-list')
      .set('Authorization', fakeToken)
      .expect(200);

    expect(res.body).to.deepEqual([
      {
        id: 'user-2',
        firstName: 'Alice',
        lastName: 'Smith',
        email: '<EMAIL>',
      },
    ]);
    sinon.assert.calledOnce(userHelperServiceStub.getUserLists);
    const [passedFilter] = userHelperServiceStub.getUserLists.getCall(0).args;
    expect(passedFilter).to.equal(undefined);
  });

  it('GET /users/userLists with filter passes filter to helper', async () => {
    const fakeToken = getToken([PermissionKey.ViewTenantUser], false);
    const filter = {where: {firstName: 'Alice'}};
    await client
      .get('/users/user-list')
      .query({filter: JSON.stringify(filter)})
      .set('Authorization', fakeToken)
      .expect(200);

    const [passedFilter] = userHelperServiceStub.getUserLists.getCall(1).args;
    expect(passedFilter).to.deepEqual(filter);
  });
});

// Acceptance tests for /users/count (getUserCount)
describe('UserController getUserCount Acceptance', () => {
  let app: AuthenticationFacadeApplication;
  let client: Client;
  let userHelperServiceStub: {getUsersCount: sinon.SinonStub};

  before('setupApplication', async () => {
    const setup = await setupApplication();
    app = setup.app;
    client = setup.client;

    userHelperServiceStub = {
      getUsersCount: sinon.stub().resolves({count: 5}),
    };

    app.bind('services.UserHelperService').to(userHelperServiceStub);
  });

  after(async () => {
    await app.stop();
  });

  it('GET /users/count returns user count from helper', async () => {
    const fakeToken = getToken([PermissionKey.ViewTenantUser], false);
    const res = await client
      .get('/users/count')
      .set('Authorization', fakeToken)
      .expect(200);

    expect(res.body).to.deepEqual({count: 5});
    sinon.assert.calledOnce(userHelperServiceStub.getUsersCount);
    const [passedWhere] = userHelperServiceStub.getUsersCount.getCall(0).args;
    expect(passedWhere).to.equal(undefined);
  });

  it('GET /users/count with where passes where to helper', async () => {
    const fakeToken = getToken([PermissionKey.ViewTenantUser], false);
    const where = {firstName: 'Alice'};
    await client
      .get('/users/count')
      .query({where: JSON.stringify(where)})
      .set('Authorization', fakeToken)
      .expect(200);

    const [passedWhere] = userHelperServiceStub.getUsersCount.getCall(1).args;
    expect(passedWhere).to.deepEqual(where);
  });
});
