import {Client, expect, sinon} from '@loopback/testlab';
import {setupApplication, getToken} from './test-helper';
import {UserTenantServiceHelper} from '../../services';
import {AuthenticationFacadeApplication} from '../../application';
import {Filter, Where} from '@loopback/repository';
import {PermissionKey} from '@local/core';

describe('RoleViewController Acceptance', () => {
  let app: AuthenticationFacadeApplication;
  let client: Client;
  let userTenantServiceStub: Partial<UserTenantServiceHelper>;

  // Mock response for getRoles
  const mockTenantUserResponse = {
    roleId: '1',
    userId: '1',
    tenantId: '1',
    roleName: 'admin',
    userTenantId: '1',
  };

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());

    // Create a stubbed version of UserTenantServiceHelper
    userTenantServiceStub = {
      getRoles: sinon.stub().resolves([mockTenantUserResponse]),
      getRolesCount: sinon.stub().resolves({count: 1}),
    };

    // Bind stubbed service to override the real one
    app.bind('services.UserTenantServiceHelper').to(userTenantServiceStub);
  });

  after(async () => {
    await app.stop();
  });

  it('GET /tenants/role-view - should return roles', async () => {
    const filter: Filter<object> = {
      where: {
        tenantId: '1',
      },
    };

    const res = await client
      .get('/tenants/role-view')
      .query(filter)
      .set('Authorization', getToken([PermissionKey.ViewRoles]))
      .expect(200);

    expect(res.body).to.be.an.Array();
    expect(res.body[0]).to.containEql(mockTenantUserResponse);
    sinon.assert.calledOnce(userTenantServiceStub.getRoles as sinon.SinonStub);
  });

  it('GET /tenants/role-view/count - should return count', async () => {
    const where: Where<object> = {
      tenantId: '1',
    };

    const res = await client
      .get('/tenants/role-view/count')
      .query({where})
      .set('Authorization', getToken([PermissionKey.ViewRoles]))
      .expect(200);

    expect(res.body).to.containEql({count: 1});
    sinon.assert.calledOnce(
      userTenantServiceStub.getRolesCount as sinon.SinonStub,
    );
  });

  it('should reject requests without authorization token', async () => {
    await client.get('/tenants/role-view').expect(401);
    await client.get('/tenants/role-view/count').expect(401);
  });

  it('should reject requests with invalid permissions', async () => {
    await client
      .get('/tenants/role-view')
      .set('Authorization', getToken([]))
      .expect(403);

    await client
      .get('/tenants/role-view/count')
      .set('Authorization', getToken([]))
      .expect(403);
  });
});
