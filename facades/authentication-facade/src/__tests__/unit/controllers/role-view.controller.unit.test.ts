import {expect} from '@loopback/testlab';
import sinon from 'sinon';
import {RoleViewController} from '../../../controllers/role-view.controller';
import {UserTenantServiceHelper} from '../../../services';
import {Filter, Count, Where} from '@loopback/repository';
import {RoleView} from '@local/core';
import {Request} from '@loopback/rest';

describe('RoleViewController (unit)', () => {
  let controller: RoleViewController;
  let userTenantService: sinon.SinonStubbedInstance<UserTenantServiceHelper>;
  const mockToken = 'Bearer dummy-token';
  const mockRequest = {
    headers: {
      authorization: mockToken,
    },
  } as Request;

  beforeEach(() => {
    userTenantService = sinon.createStubInstance(UserTenantServiceHelper);
    controller = new RoleViewController(
      mockRequest,
      userTenantService as unknown as UserTenantServiceHelper,
    );
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('constructor', () => {
    it('should throw error when authorization token is missing', () => {
      const requestWithoutAuth = {
        headers: {},
      } as Request;

      expect(() => {
        new RoleViewController(
          requestWithoutAuth,
          userTenantService as unknown as UserTenantServiceHelper,
        );
      }).to.throw('Authorization token is missing');
    });
  });

  describe('getRoles', () => {
    it('should return roles when called with filter', async () => {
      const mockFilter: Filter<RoleView> = {
        where: {
          tenantId: 'mocked-tenant-id',
        },
      };

      const mockRoles = [
        {
          roleId: '1',
          tenantId: 'mocked-tenant-id',
        },
      ] as RoleView[];

      userTenantService.getRoles.resolves(mockRoles);

      const result = await controller.getRoles(mockFilter);

      expect(result).to.deepEqual(mockRoles);
      sinon.assert.calledWith(
        userTenantService.getRoles,
        mockToken,
        mockFilter,
      );
    });

    it('should return roles when called without filter', async () => {
      const mockRoles = [
        {
          roleId: '1',
          tenantId: 'mocked-tenant-id',
        },
      ] as RoleView[];

      userTenantService.getRoles.resolves(mockRoles);

      const result = await controller.getRoles();

      expect(result).to.deepEqual(mockRoles);
      sinon.assert.calledWith(userTenantService.getRoles, mockToken, undefined);
    });
  });

  describe('getRolesCount', () => {
    it('should return count when called with where clause', async () => {
      const mockWhere: Where<RoleView> = {
        tenantId: 'mocked-tenant-id',
      };

      const mockCount: Count = {
        count: 5,
      };

      userTenantService.getRolesCount.resolves(mockCount);

      const result = await controller.getRolesCount(mockWhere);

      expect(result).to.deepEqual(mockCount);
      sinon.assert.calledWith(
        userTenantService.getRolesCount,
        mockToken,
        mockWhere,
      );
    });

    it('should return count when called without where clause', async () => {
      const mockCount: Count = {
        count: 3,
      };

      userTenantService.getRolesCount.resolves(mockCount);

      const result = await controller.getRolesCount();

      expect(result).to.deepEqual(mockCount);
      sinon.assert.calledWith(
        userTenantService.getRolesCount,
        mockToken,
        undefined,
      );
    });
  });
});
