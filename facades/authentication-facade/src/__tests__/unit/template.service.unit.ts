import {expect} from '@loopback/testlab';
import {TemplateService} from '../../services/template.service'; // Adjust the import path as per your project

describe('TemplateService', () => {
  let templateService: TemplateService;

  beforeEach(() => {
    templateService = new TemplateService();
  });

  it('should correctly compile and return the template with provided data', () => {
    const template = 'Hello, {{name}}! Your order #{{orderId}} is confirmed.';
    const data = {
      name: '<PERSON>',
      orderId: 12345,
    };

    const result = templateService.generateEmail(template, data);

    expect(result).to.equal('Hello, <PERSON>! Your order #12345 is confirmed.');
  });

  it('should return the template with placeholders if data is missing', () => {
    const template = 'Hello, {{name}}!';
    const data = {}; // No 'name' provided

    const result = templateService.generateEmail(template, data);

    // Handlebars will leave the placeholder blank if not provided
    expect(result).to.equal('Hello, !');
  });

  it('should handle empty template string', () => {
    const template = '';
    const data = {
      name: '<PERSON>',
    };

    const result = templateService.generateEmail(template, data);

    expect(result).to.equal('');
  });

  it('should handle numeric values correctly in the template', () => {
    const template = 'Your lucky number is {{luckyNumber}}.';
    const data = {
      luckyNumber: 7,
    };

    const result = templateService.generateEmail(template, data);

    expect(result).to.equal('Your lucky number is 7.');
  });
});
