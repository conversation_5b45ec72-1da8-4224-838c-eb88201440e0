import {expect} from '@loopback/testlab';
import {AuthenticatorService} from '../../services/authenticator.service';
import {UserTokenRepository} from '../../repositories/user-token.repository';
import {PermissionKey} from '@local/core';
import sinon from 'sinon';
import {UserView} from '@sourceloop/user-tenant-service';

type PrivateMethod = <T extends object>(payload: T, expiry?: number) => string;

describe('AuthenticatorService (unit)', () => {
  let authenticatorService: AuthenticatorService;
  let userTokenRepoStub: sinon.SinonStubbedInstance<UserTokenRepository>;
  const OLD_ENV = process.env;

  beforeEach(() => {
    process.env = {...OLD_ENV};
    process.env.JWT_SECRET = 'dummy-secret';
    process.env.JWT_ISSUER = 'test-issuer';
    process.env.FORGET_PASSWORD_KEY_LENGTH = '16';
    process.env.FORGET_PASSWORD_LINK_EXPIRY = '300';

    userTokenRepoStub = sinon.createStubInstance(UserTokenRepository);

    authenticatorService = new AuthenticatorService(userTokenRepoStub);
  });

  afterEach(() => {
    sinon.restore();
    process.env = OLD_ENV;
  });

  it('should generate and store forget password temp token', async () => {
    const user = {
      id: 'user-1',
      email: '<EMAIL>',
    } as UserView;

    const tokenSpy = sinon.spy(
      authenticatorService as unknown as {_generateTempToken: PrivateMethod},
      '_generateTempToken',
    );

    const randomKey =
      await authenticatorService.setForgetPasswordTempToken(user);

    expect(randomKey).to.be.String();
    sinon.assert.calledOnce(userTokenRepoStub.set);
    sinon.assert.calledOnce(userTokenRepoStub.expire);
    sinon.assert.calledOnce(tokenSpy);

    const [payload] = tokenSpy.firstCall.args;
    expect(
      (payload as {permissions: (typeof PermissionKey)[]}).permissions,
    ).to.deepEqual([PermissionKey.UpdatePassword]);
  });

  it('should throw if JWT_SECRET is not defined in _generateTempToken', () => {
    delete process.env.JWT_SECRET;

    const payload = {test: true};
    expect(() =>
      (
        authenticatorService as unknown as {_generateTempToken: PrivateMethod}
      )._generateTempToken(payload),
    ).to.throw('JWT_SECRET is not defined in the environment variables.');
  });

  it('should generate a signed token in _generateTempToken', () => {
    const payload = {foo: 'bar'};
    const token = (
      authenticatorService as unknown as {_generateTempToken: PrivateMethod}
    )._generateTempToken(payload, 100);
    expect(token).to.be.String();
  });
});
