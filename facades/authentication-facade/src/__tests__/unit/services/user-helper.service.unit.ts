// Unit tests for UserHelperService
import {expect, sinon} from '@loopback/testlab';
import {RestBindings, HttpErrors, Request} from '@loopback/rest';
import {UserTenantProxyService} from '../../../../src/services/proxies';
import {Context} from '@loopback/context';
import {UserView} from '@sourceloop/user-tenant-service';
import {UserHelperService} from '../../../services';

describe('UserHelperService', () => {
  let userProxyServiceStub: sinon.SinonStubbedInstance<UserTenantProxyService>;
  let request: Request;
  let ctx: Context;

  beforeEach(() => {
    userProxyServiceStub = {
      find: sinon.stub(),
      getRoleView: sinon.stub(),
      getRoleViewCount: sinon.stub(),
      getUserCount: sinon.stub(),
      getUsersList: sinon.stub(),
    };
    ctx = new Context();
    ctx.bind('services.UserProxyService').to(userProxyServiceStub);
  });

  it('throws Unauthorized if authorization header missing', () => {
    request = {headers: {}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);
    expect(() => {
      new UserHelperService(request, userProxyServiceStub);
    }).to.throw(HttpErrors.Unauthorized);
  });

  it('calls getUsersCount and returns result', async () => {
    process.env.DEFAULT_TENANT_ID = 'tenant1';
    process.env.SUPER_ADMIN_ROLE_NAME = 'superadmin';
    request = {headers: {authorization: 'Bearer token123'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    userProxyServiceStub.getUserCount.resolves({count: 7});

    const service = new UserHelperService(request, userProxyServiceStub);
    const result = await service.getUsersCount({firstName: 'John'});
    expect(result).to.deepEqual({count: 7});
    sinon.assert.calledOnce(userProxyServiceStub.getUserCount);
    const [token, tenantId, where] =
      userProxyServiceStub.getUserCount.getCall(0).args;
    expect(token).to.equal('Bearer token123');
    expect(tenantId).to.equal('tenant1');
    if (typeof where === 'string') {
      const parsed = JSON.parse(where);
      expect(parsed).to.have.property('and');
      expect(parsed.and[1]).to.deepEqual({roleName: {neq: 'superadmin'}});
    }
  });

  it('getUsersCount throws InternalServerError if DEFAULT_TENANT_ID missing', async () => {
    delete process.env.DEFAULT_TENANT_ID;
    request = {headers: {authorization: 'Bearer token123'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    const service = new UserHelperService(request, userProxyServiceStub);
    await expect(service.getUsersCount()).to.be.rejectedWith(
      HttpErrors.InternalServerError,
    );
  });

  it('calls getUserLists and returns result', async () => {
    process.env.DEFAULT_TENANT_ID = 'tenant2';
    process.env.SUPER_ADMIN_ROLE_NAME = 'superadmin';
    request = {headers: {authorization: 'Bearer token456'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    const userView = new UserView({
      id: '1',
      firstName: 'A',
      lastName: 'X',
      username: 'ax',
      email: '<EMAIL>',
      authClientIds: '',
      roleName: 'user',
      status: undefined,
      createdOn: new Date(),
      createdBy: 'system',
      phone: '',
      tenantId: 'tenant2',
      defaultTenantId: 'tenant2',
      roleId: 'role1',
      tenantName: 'Tenant X',
      userTenantId: 'utid1',
      lastLogin: undefined,
    });

    const userView2 = new UserView({
      id: '2',
      firstName: 'B',
      lastName: 'Y',
      username: 'by',
      email: '<EMAIL>',
      authClientIds: '',
      roleName: 'user',
      status: undefined,
      createdOn: new Date(),
      createdBy: 'system',
      phone: '',
      tenantId: 'tenant2',
      defaultTenantId: 'tenant2',
      roleId: 'role2',
      tenantName: 'Tenant Y',
      userTenantId: 'utid2',
      lastLogin: undefined,
    });

    userProxyServiceStub.getUsersList.resolves([userView, userView2]);

    const service = new UserHelperService(request, userProxyServiceStub);
    const result = await service.getUserLists({where: {firstName: 'A'}});
    expect(result).to.have.length(2);
    sinon.assert.calledOnce(userProxyServiceStub.getUsersList);
    const [token, tenantId, filter] =
      userProxyServiceStub.getUsersList.getCall(0).args;
    expect(token).to.equal('token456');
    expect(tenantId).to.equal('tenant2');
    if (
      filter &&
      typeof filter !== 'string' &&
      filter.where &&
      Array.isArray((filter.where as {and?: unknown[]}).and)
    ) {
      const andArr = (filter.where as {and?: unknown[]}).and as unknown[];
      expect(andArr[1]).to.deepEqual({roleName: {neq: 'superadmin'}});
    }
  });

  it('getUserLists throws InternalServerError if DEFAULT_TENANT_ID missing', async () => {
    delete process.env.DEFAULT_TENANT_ID;
    request = {headers: {authorization: 'Bearer token456'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    const service = new UserHelperService(request, userProxyServiceStub);
    await expect(service.getUserLists()).to.be.rejectedWith(
      HttpErrors.InternalServerError,
    );
  });

  it('getUserLists extracts access token part from authorization header', async () => {
    process.env.DEFAULT_TENANT_ID = 'tenant3';
    process.env.SUPER_ADMIN_ROLE_NAME = 'superadmin';
    request = {headers: {authorization: 'Bearer token789'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    userProxyServiceStub.getUsersList.resolves([]);

    const service = new UserHelperService(request, userProxyServiceStub);
    await service.getUserLists();
    const [token] = userProxyServiceStub.getUsersList.getCall(0).args;
    expect(token).to.equal('token789');
  });
});
