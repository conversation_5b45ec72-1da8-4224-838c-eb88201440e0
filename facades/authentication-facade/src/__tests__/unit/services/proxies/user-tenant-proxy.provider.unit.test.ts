import {expect} from '@loopback/testlab';
import sinon from 'sinon';
import {
  UserTenantProxyServiceProvider,
  IUserView,
} from '../../../../services/proxies/user-tenant-proxy.provider';
import {UserTenantServiceDataSource} from '../../../../datasources/proxies/user-tenant-proxy.datasource';
import {Filter, Count} from '@loopback/repository';
import {User} from '@sourceloop/authentication-service';

describe('UserTenantProxyServiceProvider (unit)', () => {
  let provider: UserTenantProxyServiceProvider;
  let mockService: {
    find: sinon.SinonStub;
    getRoleView: sinon.SinonStub;
    getRoleViewCount: sinon.SinonStub;
    getUserCount: sinon.SinonStub;
    getUsersList: sinon.SinonStub;
  };

  beforeEach(() => {
    mockService = {
      find: sinon.stub().resolves([]),
      getRoleView: sinon.stub().resolves([]),
      getRoleViewCount: sinon.stub().resolves({count: 0}),
      getUserCount: sinon.stub().resolves({count: 0}),
      getUsersList: sinon.stub().resolves([]),
    };

    const mockDataSource = {
      name: 'UserTenantService',
      connector: 'rest',
      settings: {
        baseURL: 'http://example.com',
        crud: false,
      },
      dataSource: 'UserTenantService',
    } as unknown as UserTenantServiceDataSource;

    // Add async value method to provider that returns our mock service
    provider = new UserTenantProxyServiceProvider(mockDataSource);
    sinon.stub(provider, 'value').resolves(mockService);
  });

  afterEach(() => {
    sinon.restore();
  });

  it('should be instantiated with datasource', () => {
    expect(provider).to.be.instanceOf(UserTenantProxyServiceProvider);
  });

  describe('service methods', () => {
    const mockToken = 'dummy-token';
    const mockTenantId = 'dummy-tenant-id';

    beforeEach(async () => {
      await provider.value();
    });

    it('should call find method with correct parameters', async () => {
      const mockFilter: Filter<IUserView> = {
        where: {id: 'dummy-id'},
      };
      const expectedUsers = [
        {id: 'dummy-id', email: '<EMAIL>'},
      ] as User[];

      mockService.find.resolves(expectedUsers);

      const result = await mockService.find(
        mockToken,
        mockTenantId,
        JSON.stringify(mockFilter),
      );

      expect(result).to.eql(expectedUsers);
      sinon.assert.calledWith(
        mockService.find,
        mockToken,
        mockTenantId,
        JSON.stringify(mockFilter),
      );
    });

    it('should call getRoleView method with correct parameters', async () => {
      const mockFilter = {
        where: {roleId: 'dummy-role'},
      };
      const expectedRoles = [{roleId: 'dummy-role', tenantId: mockTenantId}];

      mockService.getRoleView.resolves(expectedRoles);

      const result = await mockService.getRoleView(
        mockToken,
        mockTenantId,
        JSON.stringify(mockFilter),
      );

      expect(result).to.eql(expectedRoles);
      sinon.assert.calledWith(
        mockService.getRoleView,
        mockToken,
        mockTenantId,
        JSON.stringify(mockFilter),
      );
    });

    it('should call getRoleViewCount method with correct parameters', async () => {
      const mockWhere = {
        roleId: 'dummy-role',
      };
      const expectedCount: Count = {
        count: 5,
      };

      mockService.getRoleViewCount.resolves(expectedCount);

      const result = await mockService.getRoleViewCount(
        mockToken,
        mockTenantId,
        JSON.stringify(mockWhere),
      );

      expect(result).to.eql(expectedCount);
      sinon.assert.calledWith(
        mockService.getRoleViewCount,
        mockToken,
        mockTenantId,
        JSON.stringify(mockWhere),
      );
    });
  });
});
