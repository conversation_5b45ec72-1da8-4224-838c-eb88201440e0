import {HttpErrors} from '@loopback/rest';
import {expect} from '@loopback/testlab';
import sinon from 'sinon';
import {UserToken} from '../../models';
import {ForgetPasswordTokenVerifierProvider} from '../../providers';
import {UserTokenRepository} from '../../repositories/user-token.repository';

describe('ForgetPasswordTokenVerifierProvider', () => {
  let provider: ForgetPasswordTokenVerifierProvider;
  let userTokenRepoStub: sinon.SinonStubbedInstance<UserTokenRepository>;

  const mockToken = 'mock.jwt.token';

  beforeEach(() => {
    userTokenRepoStub = sinon.createStubInstance(UserTokenRepository);
    provider = new ForgetPasswordTokenVerifierProvider(
      userTokenRepoStub as unknown as UserTokenRepository,
    );
    process.env.JWT_SECRET = 'test-secret';
    process.env.JWT_ISSUER = 'test-issuer';
  });

  afterEach(() => {
    sinon.restore();
    delete process.env.JWT_SECRET;
    delete process.env.JWT_ISSUER;
  });

  it('should throw Unauthorized error if token not found in DB', async () => {
    userTokenRepoStub.get.resolves(new UserToken({token: undefined}));

    const fn = await provider.value();
    try {
      await fn('invalid-token');
      throw new Error('Expected Unauthorized error to be thrown');
    } catch (err) {
      expect(err).instanceOf(HttpErrors.Unauthorized);
    }
  });

  it('should throw error if JWT_SECRET is not defined', async () => {
    delete process.env.JWT_SECRET;
    userTokenRepoStub.get.resolves(new UserToken({token: mockToken}));

    const providerWithoutSecret = new ForgetPasswordTokenVerifierProvider(
      userTokenRepoStub as unknown as UserTokenRepository,
    );

    const fn = await providerWithoutSecret.value();

    try {
      await fn('some-token');
    } catch (err) {
      expect(err.message).to.equal(
        'JWT_SECRET is not defined in the environment variables.',
      );
    }
  });
});
