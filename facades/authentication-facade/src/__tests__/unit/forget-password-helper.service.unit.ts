import {
  NotificationTemplates,
  UpdatePasswordDto as ServiceUpdatePasswordDto,
} from '@local/core';
import {Request} from '@loopback/rest';
import {expect} from '@loopback/testlab';
import sinon, {SinonStub} from 'sinon';
import {ForgetPasswordRequestDto, UpdatePasswordDto} from '../../models/dto';
import {UserTokenRepository} from '../../repositories/user-token.repository';
import {AuthenticatorService} from '../../services/authenticator.service';
import {CryptoHelperService} from '../../services/crypto-helper.service';
import {ForgetPasswordHelperService} from '../../services/forget-password-helper.service';
import {
  NotificationProxyService,
  UserTenantProxyService,
} from '../../services/proxies';
import {AuthenticationProxyService} from '../../services/proxies/authentication-proxy.provider';
import {TemplateService} from '../../services/template.service';
import {IAuthUserWithPermissions} from '@sourceloop/core';

const FAKE_USER = {
  id: 'user-id',
  email: '<EMAIL>',
};

describe('ForgetPasswordHelperService (unit)', () => {
  let service: ForgetPasswordHelperService;

  // Manual stubs for interfaces
  let authProxyServiceStub: AuthenticationProxyService;
  let userTenantServiceStub: UserTenantProxyService;
  let cryptoHelperStub: CryptoHelperService;
  let notificationServiceStub: NotificationProxyService;
  let templateServiceStub: TemplateService;
  let authenticatorServiceStub: AuthenticatorService;
  let userTokenRepoStub: UserTokenRepository;

  let mockRequest: Partial<Request>;
  let currentUser: IAuthUserWithPermissions;

  beforeEach(() => {
    // Interface method stubs using sinon.stub()
    authProxyServiceStub = {
      login: sinon.stub(),
      getToken: sinon.stub(),
      me: sinon.stub(),
      logout: sinon.stub(),
      forgetPassword: sinon.stub(),
      updatePassword: sinon.stub().resolves(),
      refreshToken: sinon.stub(),
    };

    userTenantServiceStub = {
      find: sinon.stub(),
      getRoleView: sinon.stub(),
      getRoleViewCount: sinon.stub(),
      getUsersList: sinon.stub(),
      getUserCount: sinon.stub(),
    };

    cryptoHelperStub = {
      generateTempToken: sinon.stub().returns('mocked-token'),
      generateRandomString: sinon.stub(),
    } as CryptoHelperService;

    notificationServiceStub = {
      createNotification: sinon.stub().resolves({}),
      getTemplateByName: sinon.stub().resolves({
        subject: 'Test Subject',
        body: 'Test Body',
      } as NotificationTemplates),
    };

    templateServiceStub = {
      generateEmail: sinon.stub().returns('Email content with link'),
    } as TemplateService;

    authenticatorServiceStub = {
      setForgetPasswordTempToken: sinon.stub().resolves('reset-token'),
    } as unknown as AuthenticatorService;

    userTokenRepoStub = {
      delete: sinon.stub().resolves(),
    } as unknown as UserTokenRepository;

    mockRequest = {
      headers: {authorization: 'Bearer some-token'},
    };

    currentUser = {
      id: 'user-id',
      userTenantId: 'tenant-user-id',
      tenantId: 'tenant-id',
      permissions: [],
      authClientId: 123, // Use a number instead of a string
      role: 'user',
      firstName: 'Test',
      lastName: 'User',
      username: 'testuser',
    };

    process.env.DEFAULT_TENANT_ID = 'tenant-id';
    process.env.CLIENT_ID = 'client-id';
    process.env.CLIENT_SECRET = 'client-secret';
    process.env.RESET_LINK = 'https://reset.example.com';
    process.env.NOTIFICATION_FROM_EMAIL = '<EMAIL>';

    service = new ForgetPasswordHelperService(
      authProxyServiceStub,
      userTenantServiceStub,
      cryptoHelperStub,
      notificationServiceStub,
      templateServiceStub,
      authenticatorServiceStub,
      mockRequest as Request,
      currentUser,
      userTokenRepoStub,
    );
  });

  it('sends reset password email if user exists', async () => {
    (userTenantServiceStub.find as SinonStub).resolves([FAKE_USER]);

    const dto = new ForgetPasswordRequestDto({email: '<EMAIL>'});
    await service.forgetPassword(dto);

    sinon.assert.calledWith(
      templateServiceStub.generateEmail as SinonStub,
      'Test Body',
      {
        RESET_LINK: 'https://reset.example.com?code=reset-token',
      },
    );

    sinon.assert.called(
      notificationServiceStub.createNotification as SinonStub,
    );
  });

  it('returns successfully if no user found', async () => {
    (userTenantServiceStub.find as SinonStub).resolves([]);

    const dto = new ForgetPasswordRequestDto({email: '<EMAIL>'});
    await service.forgetPassword(dto);

    sinon.assert.notCalled(templateServiceStub.generateEmail as SinonStub);
    sinon.assert.notCalled(
      notificationServiceStub.createNotification as SinonStub,
    );
  });

  it('updates password and deletes token', async () => {
    const dto = new UpdatePasswordDto({newPassword: 'newpass'});

    await service.updatePassword(dto);

    sinon.assert.calledWith(
      authProxyServiceStub.updatePassword as sinon.SinonStub,
      'mocked-token',
      sinon.match.instanceOf(ServiceUpdatePasswordDto),
    );

    sinon.assert.calledWith(
      userTokenRepoStub.delete as SinonStub,
      'some-token',
    );
  });

  it('throws error if CLIENT_ID is missing', async () => {
    delete process.env.CLIENT_ID;
    const dto = new UpdatePasswordDto({newPassword: 'newpass'});

    await expect(service.updatePassword(dto)).to.be.rejectedWith(
      'CLIENT_ID or CLIENT_SECRET is not defined in environment variables',
    );
  });
});
