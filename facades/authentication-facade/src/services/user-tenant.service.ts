import {BindingScope, inject, injectable} from '@loopback/core';
import {UserTenantProxyService} from './proxies';
import {param} from '@loopback/openapi-v3';
import {RoleView} from '@local/core';
import {Count, Filter, Where} from '@loopback/repository';

@injectable({scope: BindingScope.SINGLETON})
export class UserTenantServiceHelper {
  defaultTenantId: string =
    process.env.DEFAULT_TENANT_ID ??
    (() => {
      throw new Error('DEFAULT_TENANT_ID is not set');
    })();

  /**
   * Helper service for generating and storing temporary authentication tokens,
   * specifically for forget password flows.
   */
  /**
   * Constructs a new instance of the UserTenantServiceHelper.
   *
   * @param userTokenRepository - Repository to store and retrieve user tokens.
   */
  constructor(
    @inject(`services.UserTenantProxyService`)
    private readonly userTenantProxyService: UserTenantProxyService,
  ) {}

  /**
   * Get roles by tenant user group
   * @param token
   * @param filter
   * @returns
   */
  async getRoles(
    @param.header.string('Authorization') token: string,
    @param.filter(RoleView) filter?: Filter<RoleView>,
  ): Promise<RoleView[]> {
    return this.userTenantProxyService.getRoleView(
      token,
      this.defaultTenantId,
      filter ? JSON.stringify(filter) : undefined,
    );
  }

  /**
   * Get roles count by tenant user group
   * @param token
   * @param where
   * @returns
   */
  async getRolesCount(
    @param.header.string('Authorization') token: string,
    @param.where(RoleView)
    where?: Where<RoleView>,
  ): Promise<Count> {
    const whereObj = {
      ...where,
      tenantId: this.defaultTenantId,
    };
    const jsonWhere = JSON.stringify(whereObj);
    return this.userTenantProxyService.getRoleViewCount(
      token,
      this.defaultTenantId,
      jsonWhere,
    );
  }
}
