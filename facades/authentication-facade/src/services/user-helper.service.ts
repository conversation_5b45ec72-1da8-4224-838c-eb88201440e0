import {BindingScope, inject, injectable} from '@loopback/context';
import {AnyObject, Count, Filter, Where} from '@loopback/repository';
import {HttpErrors, Request, RestBindings} from '@loopback/rest';
import {UserView} from '@sourceloop/user-tenant-service';
import {UserTenantProxyService} from './proxies';

/**
 * Service to assist with user-related operations, such as fetching user lists and counts,
 * while handling authorization and filtering out super admin users.
 */
@injectable({scope: BindingScope.TRANSIENT})
export class UserHelperService {
  /**
   * The authorization token extracted from the request headers.
   */
  token: string;

  /**
   * Constructs a new UserHelperService.
   * Extracts the authorization token from the request headers and injects the UserProxyService.
   * @param request - The current HTTP request object.
   * @param userProxyService - The proxy service for user operations.
   * @throws {HttpErrors.Unauthorized} If the authorization header is missing.
   */
  constructor(
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @inject('services.UserTenantProxyService')
    private readonly userProxyService: UserTenantProxyService,
  ) {
    // Extract the authorization token from the request headers
    if (this.request.headers.authorization) {
      this.token = this.request.headers.authorization;
    } else {
      // Throw unauthorized error if token is missing
      throw new HttpErrors.Unauthorized();
    }
  }

  /**
   * Retrieves the count of users, excluding those with the super admin role.
   * @param where - Optional filter criteria for users.
   * @returns A promise resolving to the count of users.
   * @throws {HttpErrors.InternalServerError} If DEFAULT_TENANT_ID is not set.
   */
  async getUsersCount(where?: Where<UserView>): Promise<Count> {
    if (!process.env.DEFAULT_TENANT_ID) {
      throw new HttpErrors.InternalServerError(
        'DEFAULT_TENANT_ID is not defined in environment variables',
      );
    }
    // Prepare filter to exclude super admin users from the count
    const filterwhere = (where ?? {}) as AnyObject;
    const whereObj = {
      and: [
        ...(filterwhere.and ?? (filterwhere ? [filterwhere] : [])),
        {roleName: {neq: process.env.SUPER_ADMIN_ROLE_NAME!}},
      ],
    };
    const jsonWhere = JSON.stringify(whereObj);
    // Delegate the count operation to the UserProxyService
    return this.userProxyService.getUserCount(
      this.token,
      process.env.DEFAULT_TENANT_ID,
      jsonWhere,
    );
  }

  /**
   * Retrieves a list of users, excluding those with the super admin role.
   * @param filter - Optional filter criteria for querying users.
   * @returns A promise resolving to an array of UserView objects.
   * @throws {HttpErrors.InternalServerError} If DEFAULT_TENANT_ID is not set.
   */
  async getUserLists(filter?: Filter<UserView>): Promise<UserView[]> {
    if (!process.env.DEFAULT_TENANT_ID) {
      throw new HttpErrors.InternalServerError(
        'DEFAULT_TENANT_ID is not defined in environment variables',
      );
    }
    // Prepare filter to exclude super admin users from the list
    const filterWhere = (filter?.where ?? {}) as AnyObject;
    const refinedFilter: Filter<UserView> = {
      ...filter,
      where: {
        and: [
          ...(filterWhere.and ?? (filter?.where ? [filter.where] : [])),
          {roleName: {neq: process.env.SUPER_ADMIN_ROLE_NAME!}},
        ],
      },
    };
    // Extract the access token part from the authorization header
    const accessToken = this.token?.split(' ')[1];
    // Delegate the list operation to the UserProxyService
    return this.userProxyService.getUsersList(
      accessToken,
      process.env.DEFAULT_TENANT_ID,
      refinedFilter,
    );
  }
}
