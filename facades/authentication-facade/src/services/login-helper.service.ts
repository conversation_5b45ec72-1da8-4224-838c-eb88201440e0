import {BindingScope, inject, injectable} from '@loopback/context';
import {service} from '@loopback/core';
import {HttpErrors, Request, RestBindings} from '@loopback/rest';
import {
  AuthRefreshTokenRequest,
  AuthTokenRequest as AuthTokenRequestWithClient,
  LoginRequest as LoginRequestWithClient,
  RefreshTokenRequest,
  TokenResponse,
} from '@sourceloop/authentication-service';
import {AuthTokenRequest, LoginRequest} from '../models';
import {
  AuthenticationProxyService,
  AuthenticationProxyServiceProvider,
} from './proxies/authentication-proxy.provider';

const SKIP = 7;
@injectable({scope: BindingScope.TRANSIENT})
export class LoginHelperService {
  constructor(
    @service(AuthenticationProxyServiceProvider)
    private readonly authProxyService: AuthenticationProxyService,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {}

  /**
   * Refreshes the authentication token using the provided refresh token and request data.
   *
   * @param token - The current authentication token.
   * @param req - The refresh token request object, excluding the tenant ID.
   * @returns A promise resolving to the result of the refresh token operation.
   */
  async refreshToken(
    token: string,
    req: Omit<AuthRefreshTokenRequest, 'tenantId'>,
  ) {
    return this.authProxyService.refreshToken(
      token,
      new AuthRefreshTokenRequest({
        refreshToken: req.refreshToken,
        tenantId: process.env.DEFAULT_TENANT_ID,
      }),
    );
  }

  async me() {
    let token = this.request.headers.authorization;
    if (token?.startsWith('Bearer ')) {
      token = token.slice(SKIP);
    }
    if (!token) throw HttpErrors.Unauthorized();
    return this.authProxyService.me(token);
  }

  /**
   * Authenticates a user using the provided login request and retrieves an authentication token.
   *
   * This method first attempts to log in the user by forwarding the credentials to the authentication proxy service.
   * Upon successful authentication, it receives an authorization code, which is then exchanged for a token response.
   *
   * @param req - The login request containing the user's credentials.
   * @returns A promise that resolves to a {@link TokenResponse} containing the authentication token.
   * @throws Will throw an error if authentication fails or if the token cannot be retrieved.
   */
  async login(req: LoginRequest): Promise<TokenResponse> {
    const {code} = await this.authProxyService.login(
      new LoginRequestWithClient({
        // eslint-disable-next-line @typescript-eslint/naming-convention
        client_id: process.env.CLIENT_ID,

        // eslint-disable-next-line @typescript-eslint/naming-convention
        client_secret: process.env.CLIENT_SECRET,
        username: req.username,
        password: req.password,
      }),
    );

    return this.authProxyService.getToken(
      new AuthTokenRequestWithClient({
        code: code,
        clientId: process.env.CLIENT_ID as string,
      }),
    );
  }

  /**
   * Retrieves an authentication token using the provided payload.
   *
   * @param payload - The authentication token request containing the authorization code.
   * @returns A promise that resolves to a `TokenResponse` containing the authentication token.
   *
   * @remarks
   * This method constructs an `AuthTokenRequestWithClient` using the provided code and the client ID
   * from environment variables, then delegates the token retrieval to `authProxyService.getToken`.
   */
  async getToken(payload: AuthTokenRequest): Promise<TokenResponse> {
    return this.authProxyService.getToken(
      new AuthTokenRequestWithClient({
        code: payload.code,
        clientId: process.env.CLIENT_ID as string,
      }),
    );
  }

  /**
   * Logs out the user by invalidating the access token and refresh token.
   *
   * @param token - The access token of the user.
   * @param refreshTokenReq - The refresh token request containing the refresh token.
   * @returns A promise that resolves to an object indicating the logout status.
   */
  async logout(
    token: string,
    refreshTokenReq: RefreshTokenRequest,
  ): Promise<object> {
    return this.authProxyService.logout(token, refreshTokenReq);
  }
}
