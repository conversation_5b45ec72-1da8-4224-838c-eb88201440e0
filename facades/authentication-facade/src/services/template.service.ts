import {BindingScope, injectable} from '@loopback/core';
import {default as handlebars} from 'handlebars';

/**
 * Service for handling email templates.
 * This service is responsible for retrieving and compiling email templates.
 * It uses the Handlebars templating engine to compile the templates.
 */
@injectable({scope: BindingScope.SINGLETON})
export class TemplateService {
  constructor() {}
  /**
   * Generates an email from a template with the provided data.
   * @param templateBody - The body of the email template to use.
   * @param data - The data to inject into the template.
   * @returns A string representing the compiled email.
   */
  generateEmail<T extends Record<string, string | number>>(
    templateBody: string,
    data: T,
  ): string {
    const compiledTemplate = handlebars.compile(templateBody);
    return compiledTemplate(data);
  }
}
