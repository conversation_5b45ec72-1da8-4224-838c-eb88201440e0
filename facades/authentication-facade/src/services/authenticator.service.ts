import {Per<PERSON><PERSON><PERSON>} from '@local/core';
import {BindingScope, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {sign} from 'jsonwebtoken';
import {FIVE_SECONDS, generateRandomString} from '../utils';
import {UserTokenRepository} from '../repositories/user-token.repository';
import {UserView} from '@sourceloop/user-tenant-service';

/**
 * Helper service for generating and storing temporary authentication tokens,
 * specifically for forget password flows.
 */
@injectable({scope: BindingScope.SINGLETON})
export class AuthenticatorService {
  /**
   * Constructs a new instance of the AuthenticatorService.
   *
   * @param userTokenRepository - Repository to store and retrieve user tokens.
   */
  constructor(
    @repository(UserTokenRepository)
    private userTokenRepository: UserTokenRepository,
  ) {}

  /**
   * Generates a temporary JWT token for forget password flow,
   * stores it in the token repository with an expiry,
   * and returns the random key used for lookup.
   *
   * @param user - The user object for whom the token is generated.
   * @returns A unique key under which the token is stored temporarily.
   *
   * @throws Error if JWT_SECRET or key length is not defined in environment.
   */
  async setForgetPasswordTempToken(user: UserView): Promise<string> {
    const token = this._generateTempToken(
      {...user, permissions: [PermissionKey.UpdatePassword]},
      +(process.env.FORGET_PASSWORD_LINK_EXPIRY ?? '300'), // fallback to 300 seconds
    );

    const randomKey = generateRandomString(
      +process.env.FORGET_PASSWORD_KEY_LENGTH!,
    );

    await this.userTokenRepository.set(randomKey, {token});
    await this.userTokenRepository.expire(
      randomKey,
      +process.env.FORGET_PASSWORD_LINK_EXPIRY!,
    );

    return randomKey;
  }

  /**
   * Generates a signed JWT token with provided payload and expiry.
   *
   * @param payload - The payload to encode in the JWT.
   * @param expiry - Optional expiry in seconds for the token.
   * @returns The signed JWT token.
   *
   * @throws Error if `JWT_SECRET` is not defined in environment variables.
   */
  private _generateTempToken<T extends object>(
    payload: T,
    expiry?: number,
  ): string {
    const secret = process.env.JWT_SECRET;
    if (!secret) {
      throw new Error(
        'JWT_SECRET is not defined in the environment variables.',
      );
    }

    // sonarignore:start
    return sign(payload, secret, {
      // sonarignore:end
      issuer: process.env.JWT_ISSUER,
      algorithm: 'HS256',
      expiresIn: expiry ?? FIVE_SECONDS,
    });
  }
}
