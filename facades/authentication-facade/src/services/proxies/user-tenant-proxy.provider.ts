import {inject, Provider} from '@loopback/core';
import {Count, Filter} from '@loopback/repository';
import {getService} from '@loopback/service-proxy';
import {Gender, UserStatus} from '@sourceloop/core';
import {UserTenantServiceDataSource} from '../../datasources';
import {UserView} from '@sourceloop/user-tenant-service';
import {RoleView} from '@local/core';

export interface IUserView {
  id?: string;
  firstName: string;
  lastName?: string;
  middleName?: string;
  username: string;
  email: string;
  designation?: string;
  phone?: string;
  authClientIds: string;
  lastLogin?: string;
  photoUrl?: string;
  gender?: Gender; // 'M' | 'F' | 'O'
  dob?: Date;
  defaultTenantId: string;
  status?: UserStatus; // 0 to 11 (per your jsonSchema)
  tenantId: string;
  roleId: string;
  tenantName: string;
  tenantKey?: string;
  roleName?: string;
  userTenantId: string;
}

/**
 * Interface defining the contract for the UserTenantProxyService.
 *
 * @remarks
 * Provides methods to interact with the user-tenant service for finding, creating, and updating tenant users.
 */
export interface UserTenantProxyService {
  /**
   * Finds users in a tenant using a filter.
   *
   * @param token - Authorization token.
   * @param id - Tenant ID.
   * @param filter - Filter object or string for querying users.
   * @returns A promise resolving to an array of User objects.
   */
  find(
    token: string,
    id: string,
    filter?: Filter<UserView> | string,
  ): Promise<UserView[]>;

  /**
   * Get roles by tenant user group
   * @param token
   */
  getRoleView(
    token: string,
    tenantId: string,
    filter?: string,
  ): Promise<Array<RoleView>>;

  /**
   * Get roles by tenant user group count
   * @param token
   * @param tenantId
   * @param where
   */
  getRoleViewCount(
    token: string,
    tenantId: string,
    where?: string,
  ): Promise<Count>;

  /**
   * Fetches a list of users from the User Tenant Service.
   * @param token - The authentication token for the request.
   * @param id - The tenant or user identifier.
   * @param filter - Optional filter criteria for querying users.
   * @returns A promise resolving to an array of UserView objects.
   */
  getUsersList(
    token: string,
    id: string,
    filter?: Filter<UserView> | string,
  ): Promise<UserView[]>;

  /**
   * Gets the count of users for a given tenant.
   * @param token - The authentication token for the request.
   * @param tenantId - The tenant identifier.
   * @param where - Optional filter criteria as a string.
   * @returns A promise resolving to a Count object.
   */
  getUserCount(token: string, tenantId: string, where?: string): Promise<Count>;
}

/**
 * Provider class for the UserTenantProxyService.
 * Uses the UserTenantServiceDataSource to create a proxy client.
 */
export class UserTenantProxyServiceProvider
  implements Provider<UserTenantProxyService>
{
  /**
   * Constructor to inject the UserTenantService datasource.
   *
   * @param dataSource - The datasource connected to the user-tenant service.
   */
  constructor(
    @inject('datasources.UserTenantService')
    protected dataSource: UserTenantServiceDataSource,
  ) {}

  /**
   * Returns the service proxy for UserTenantProxyService.
   *
   * @returns A promise resolving to an implementation of UserTenantProxyService.
   */
  value(): Promise<UserTenantProxyService> {
    return getService(this.dataSource);
  }
}
