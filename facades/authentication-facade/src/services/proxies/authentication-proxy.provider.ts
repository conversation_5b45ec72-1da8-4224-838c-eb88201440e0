import {inject, Provider} from '@loopback/core';
import {getService} from '@loopback/service-proxy';
import {AuthenticationServiceDataSource} from '../../datasources';
import {
  AuthRefreshTokenRequest,
  AuthTokenRequest,
  AuthUser,
  CodeResponse,
  LoginRequest,
  RefreshTokenRequest,
  TokenResponse,
} from '@sourceloop/authentication-service';
import {ForgetPasswordRequestDto, UpdatePasswordDto} from '@local/core';

export interface AuthenticationProxyService {
  login(body: LoginRequest): Promise<CodeResponse>;
  getToken(body: AuthTokenRequest): Promise<TokenResponse>;
  me(token: string): Promise<AuthUser | undefined>;
  logout(token: string, refreshTokenReq: RefreshTokenRequest): Promise<object>;
  forgetPassword(
    token: string,
    body: ForgetPasswordRequestDto,
  ): Promise<{[key: string]: string}>;
  updatePassword(token: string, body: UpdatePasswordDto): Promise<void>;
  refreshToken(
    token: string,
    body: AuthRefreshTokenRequest,
  ): Promise<TokenResponse>;
}

export class AuthenticationProxyServiceProvider
  implements Provider<AuthenticationProxyService>
{
  constructor(
    @inject('datasources.AuthenticationService')
    protected dataSource: AuthenticationServiceDataSource,
  ) {}

  value(): Promise<AuthenticationProxyService> {
    return getService(this.dataSource);
  }
}
