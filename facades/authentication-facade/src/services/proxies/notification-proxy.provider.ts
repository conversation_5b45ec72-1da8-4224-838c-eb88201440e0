import {inject, JSONObject, Provider} from '@loopback/core';
import {getService} from '@loopback/service-proxy';
import {NotificationServiceDataSource} from '../../datasources';
import {NotificationTemplates} from '@local/core';

/**
 * Interface defining the contract for the NotificationProxyService.
 */
export interface NotificationProxyService {
  /**
   * Creates a notification using the provided token and body.
   *
   * @param token - Authorization token.
   * @param body - The notification payload.
   * @returns A promise resolving to the created notification as a JSONObject.
   */
  createNotification(token: string, body: object): Promise<JSONObject>;

  /**
   * Retrieves a notification template by event name and notification type.
   *
   * @param eventName - The name of the event.
   * @param notificationType - The type of notification (e.g., EMAIL, SMS).
   * @param token - Authorization token.
   * @returns A promise resolving to a NotificationTemplates object or null.
   */
  getTemplateByName(
    eventName: string,
    notificationType: number,
    token: string,
  ): Promise<NotificationTemplates | null>;
}

/**
 * Provider class for the NotificationProxyService.
 * Uses the NotificationServiceDataSource to create a proxy client.
 */
export class NotificationProxyServiceProvider
  implements Provider<NotificationProxyService>
{
  /**
   * Constructor to inject the NotificationService datasource.
   *
   * @param dataSource - The datasource connected to the notification service.
   */
  constructor(
    @inject('datasources.NotificationService')
    protected dataSource: NotificationServiceDataSource,
  ) {}

  /**
   * Returns the service proxy for NotificationProxyService.
   *
   * @returns A promise resolving to an implementation of NotificationProxyService.
   */
  value(): Promise<NotificationProxyService> {
    return getService(this.dataSource);
  }
}
