﻿// Copyright (c) 2023 Sourcefuse Technologies
//
// This software is released under the MIT License.
// https://opensource.org/licenses/MIT

import {model, property} from '@loopback/repository';
import {ModelPropertyDescriptionString} from '@sourceloop/authentication-service';
import {CoreModel} from '@sourceloop/core';

@model({
  description: 'This is the signature for login request.',
})
export class LoginRequest extends CoreModel<LoginRequest> {
  @property({
    type: 'string',
    description: ModelPropertyDescriptionString.reqStrPropDesc,
    required: true,
  })
  username: string;

  @property({
    type: 'string',
    description: ModelPropertyDescriptionString.reqStrPropDesc,
    required: true,
  })
  password: string;
}
