import {model, Model, property} from '@loopback/repository';

/**
 * Model describing the payload used to initiate the forget password flow
 * for a tenant by sending a reset password email.
 */
@model({
  description:
    'model describing payload used to send forget password email for a tenant',
})
export class ForgetPasswordRequestDto extends Model {
  /**
   * The email address of the user requesting a password reset.
   */
  @property({type: 'string', required: true})
  email: string;

  /**
   * Constructor to initialize ForgetPasswordRequestDto.
   *
   * @param data - Optional partial data to populate the DTO.
   */
  constructor(data?: Partial<ForgetPasswordRequestDto>) {
    super(data);
  }
}
