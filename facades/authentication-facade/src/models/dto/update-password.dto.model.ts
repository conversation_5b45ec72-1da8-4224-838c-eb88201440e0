import {model, Model, property} from '@loopback/repository';

/**
 * Model describing the payload used to update the password
 * during the forget password flow for a tenant.
 */
@model({
  description:
    'model describing payload used to send forget password email for a tenant',
})
export class UpdatePasswordDto extends Model {
  /**
   * The new password to be set for the user.
   *
   * @example "StrongP@ssw0rd!"
   */
  @property({type: 'string', required: true, name: 'new_password'})
  newPassword: string;

  /**
   * Constructor to initialize UpdatePasswordDto.
   *
   * @param data - Optional partial data to populate the DTO.
   */
  constructor(data?: Partial<UpdatePasswordDto>) {
    super(data);
  }
}
