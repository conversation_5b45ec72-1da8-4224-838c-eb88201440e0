import {inject, lifeCycleObserver, LifeCycleObserver} from '@loopback/core';
import {juggler} from '@loopback/repository';
import {CONTENT_TYPE} from '@sourceloop/core';
const tokenKey = 'Bearer {token}';

const config = {
  name: 'AuthService',
  connector: 'rest',
  baseURL: '',
  crud: false,
  options: {
    baseUrl: process.env.AUTH_SERVICE_URL,
    headers: {
      accept: CONTENT_TYPE.JSON,
      ['content-type']: CONTENT_TYPE.JSON,
    },
  },
  operations: [
    {
      template: {
        method: 'POST',
        url: '/auth/login',
        headers: {
          'content-type': CONTENT_TYPE.JSON,
          accept: CONTENT_TYPE.JSON,
        },
        body: '{body}',
      },
      functions: {
        login: ['body'],
      },
    },
    {
      template: {
        method: 'POST',
        url: '/auth/token',
        headers: {
          'content-type': CONTENT_TYPE.JSO<PERSON>,
          accept: CONTENT_TYPE.JSON,
        },
        body: '{body}',
      },
      functions: {
        getToken: ['body'],
      },
    },
    {
      template: {
        method: 'POST',
        url: '/auth/send-otp',
        headers: {
          Authorization: tokenKey,
        },
        body: '{body}',
      },
      functions: {
        sendOtp: ['token', 'body'],
      },
    },
    {
      template: {
        method: 'POST',
        url: '/auth/verify-otp',
        headers: {
          Authorization: tokenKey,
        },
        body: '{body}',
      },
      functions: {
        verifyOtp: ['token', 'body'],
      },
    },
    {
      template: {
        method: 'GET',
        url: '/auth/me',
        headers: {
          Authorization: tokenKey,
        },
      },
      functions: {
        me: ['token'],
      },
    },
    {
      template: {
        method: 'POST',
        url: '/logout',
        headers: {
          Authorization: '{token}',
        },
        body: '{body}',
      },
      functions: {
        logout: ['token', 'body'],
      },
    },
    {
      template: {
        method: 'POST',
        url: '/auth/token-refresh',
        headers: {
          Authorization: '{token}',
        },
        body: '{body}',
      },
      functions: {
        refreshToken: ['token', 'body'],
      },
    },
    {
      template: {
        method: 'POST',
        url: '/auth/forget-password/verify',
        headers: {
          'content-type': CONTENT_TYPE.JSON,
          accept: CONTENT_TYPE.JSON,
          Authorization: 'Bearer {token}',
        },
        body: '{body}',
      },
      functions: {
        updatePassword: ['token', 'body'],
      },
    },
  ],
};

// Observe application's life cycle to disconnect the datasource when
// application is stopped. This allows the application to be shut down
// gracefully. The `stop()` method is inherited from `juggler.DataSource`.
// Learn more at https://loopback.io/doc/en/lb4/Life-cycle.html
@lifeCycleObserver('datasource')
export class AuthenticationServiceDataSource
  extends juggler.DataSource
  implements LifeCycleObserver
{
  static readonly dataSourceName = 'AuthenticationService';
  static readonly defaultConfig = config;

  constructor(
    @inject('datasources.config.AuthenticationService', {optional: true})
    dsConfig: object = config,
  ) {
    super(dsConfig);
  }
}
