import {inject} from '@loopback/core';
import {DefaultKeyValueRepository, Entity, juggler} from '@loopback/repository';
import {UserToken} from '../models';
import {AuthCacheSourceName} from '@sourceloop/authentication-service';

/**
 * Repository to handle caching of user tokens, typically used for temporary
 * token-based workflows like password reset, OTP verification, etc.
 *
 * This repository extends LoopBack's `DefaultKeyValueRepository` and uses a
 * key-value store (e.g., Redis, Memcached) for storing token data.
 */
export class UserTokenRepository<
  T extends UserToken = UserToken,
> extends DefaultKeyValueRepository<T> {
  /**
   * Creates a new instance of UserTokenRepository.
   *
   * @param dataSource - The key-value data source (e.g., Redis) used to store tokens.
   * @param userToken - The model class representing the UserToken entity.
   */
  constructor(
    @inject(`datasources.${AuthCacheSourceName}`)
    dataSource: juggler.DataSource,

    @inject('models.UserToken')
    private readonly userToken: typeof Entity & {prototype: T},
  ) {
    super(userToken, dataSource);
  }
}
