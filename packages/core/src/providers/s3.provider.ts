/**
 * Provider class for AWS S3 integration using the `loopback4-s3` package.
 *
 * This provider injects the AWS S3 configuration and returns an instance of `S3WithSigner`,
 * which can be used to interact with AWS S3 services with signing capabilities.
 *
 * @remarks
 * - The configuration is injected via the `AWSS3Bindings.Config` binding key.
 * - If no configuration is provided, the default configuration is used.
 *
 * @example
 * ```ts
 * const s3Provider = new AwsS3Provider(config);
 * const s3WithSigner = s3Provider.value();
 * ```
 */
import {Provider, inject} from '@loopback/core';
import {S3WithSigner, AWSS3Bindings, AwsS3Config} from 'loopback4-s3';

export class AwsS3Provider implements Provider<S3WithSigner> {
  constructor(
    @inject(AWSS3Bindings.Config, {optional: true})
    private readonly config?: AwsS3Config,
  ) {}
  value(): S3WithSigner {
    return new S3WithSigner({
      ...this.config,
    });
  }
}
