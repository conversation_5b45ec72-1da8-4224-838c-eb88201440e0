import {Getter, inject, Provider} from '@loopback/context';
import {intersection} from 'lodash';
import {Request} from 'express';
import {HttpErrors, RestBindings} from '@loopback/rest';
import {CoreBindings, Context} from '@loopback/core';
import {
  AuthorizeFn,
  AuthorizationBindings,
  AuthorizationMetadata,
} from 'loopback4-authorization';
import {PermissionKey} from '../permissions';

/**
 * Provides an implementation of the AuthorizeFn used in LoopBack authorization.
 * This provider allows checking whether a user has the required permissions
 * to access a given endpoint.
 */
export class AuthorizeActionProvider implements Provider<AuthorizeFn> {
  /**
   * Constructor for AuthorizeActionProvider
   * @param getMetadata - Getter for authorization metadata from decorators
   * @param allowAlwaysPath - List of paths that are always allowed
   * @param requestContext - Context of the current HTTP request
   */
  constructor(
    @inject.getter(AuthorizationBindings.METADATA)
    private readonly getMetadata: Getter<AuthorizationMetadata>,
    @inject(AuthorizationBindings.PATHS_TO_ALLOW_ALWAYS)
    private readonly allowAlwaysPath: string[],
    @inject(RestBindings.Http.CONTEXT)
    private readonly requestContext: Context,
  ) {}

  /**
   * Returns the authorize function to be used by LoopBack
   * @returns A function that determines whether the current user is authorized
   */
  value(): AuthorizeFn {
    return (response, req) => this.action(response, req);
  }

  /**
   * Performs the authorization check
   * @param userPermissions - Permissions granted to the current user
   * @param request - Express request object (optional)
   * @returns Boolean indicating whether the user is authorized
   */
  async action(userPermissions: string[], request?: Request): Promise<boolean> {
    const metadata: AuthorizationMetadata = await this.getMetadata();

    // Allow admin access
    if (userPermissions && userPermissions.indexOf(PermissionKey.Admin) > -1) {
      return true;
    }

    // Allow for paths configured as always allowed
    if (request && this.checkIfAllowedAlways(request)) {
      return true;
    }

    // Allow if metadata explicitly permits all
    if (metadata) {
      if (metadata.permissions.indexOf('*') === 0) {
        return true;
      }
    } else {
      try {
        // Check if controller method exists
        await this.requestContext.get(CoreBindings.CONTROLLER_METHOD_NAME);
        return false;
      } catch (error) {
        throw new HttpErrors.NotFound('API not found! ' + error);
      }
    }

    // Check if user has any required permissions
    const permissionsToCheck = metadata.permissions;
    return intersection(userPermissions, permissionsToCheck).length > 0;
  }

  /**
   * Checks whether the current request path is in the always-allowed list
   * @param req - Express request object
   * @returns Boolean indicating whether access should be allowed
   */
  checkIfAllowedAlways(req: Request): boolean {
    let allowed = false;
    allowed = !!this.allowAlwaysPath.find(path => req.path.indexOf(path) === 0);
    return allowed;
  }
}
