import {expect} from '@loopback/testlab';
import * as jwt from 'jsonwebtoken';
import * as sinon from 'sinon';
import {CryptoHelperService} from '../../services/crypto-helper.service';

describe('CryptoHelperService', () => {
  let service: CryptoHelperService;
  const OLD_ENV = process.env;

  beforeEach(() => {
    service = new CryptoHelperService();
    process.env = {...OLD_ENV}; // clone env
  });

  afterEach(() => {
    sinon.restore();
    process.env = OLD_ENV; // restore env
  });

  describe('generateTempToken', () => {
    it('should generate a signed token with default expiry', () => {
      process.env.JWT_SECRET = 'my-secret';
      process.env.JWT_ISSUER = 'my-issuer';

      const payload = {userId: 'abc123'};

      const token = service.generateTempToken(payload);
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET as string,
      ) as jwt.JwtPayload & {userId: string; iss: string};

      expect(decoded.userId).to.equal('abc123');
      expect(decoded.iss).to.equal('my-issuer');
    });

    it('should generate a signed token with custom expiry', () => {
      process.env.JWT_SECRET = 'my-secret';
      process.env.JWT_ISSUER = 'issuer';
      const payload = {role: 'admin'};

      const token = service.generateTempToken(payload, 10); // 10 seconds expiry
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET as string,
      ) as jwt.JwtPayload & {role: string};

      expect(decoded.role).to.equal('admin');
    });

    it('should throw an error if JWT_SECRET is not defined', () => {
      delete process.env.JWT_SECRET;

      expect(() => service.generateTempToken({})).to.throw(
        'JWT_SECRET is not defined in the environment variables.',
      );
    });
  });

  describe('generateRandomString', () => {
    it('should generate a random string of correct length', () => {
      const length = 16;
      const result = service.generateRandomString(length);
      expect(result).to.be.a.String();
      expect(result.length).to.equal(length);
    });

    it('should generate different strings on multiple calls', () => {
      const str1 = service.generateRandomString(12);
      const str2 = service.generateRandomString(12);
      expect(str1).to.not.equal(str2);
    });
  });
});
