import {expect, sinon} from '@loopback/testlab';
import {Constructor, MetadataInspector} from '@loopback/context';
import {FileUtilBindings, IFileRequestMetadata} from '@sourceloop/file-utils';
import {FileMetadataProvider, getFileMetadata} from '../../providers';

describe('FileMetadataProvider (unit)', () => {
  let metadataStub: sinon.SinonStub;

  class TestController {
    // Dummy method to avoid empty class SonarQube issue
    dummyMethod(): void {
      // no-op
    }
  }

  beforeEach(() => {
    metadataStub = sinon.stub(MetadataInspector, 'getMethodMetadata');
  });

  afterEach(() => {
    sinon.restore();
  });

  it('returns undefined if controllerClass is not provided', () => {
    const provider = new FileMetadataProvider(
      undefined as unknown as Constructor<{}>,
      'someMethod',
    );
    const result = provider.value();
    expect(result).to.be.undefined();
    sinon.assert.notCalled(metadataStub);
  });

  it('returns undefined if methodName is not provided', () => {
    const provider = new FileMetadataProvider(
      TestController,
      undefined as unknown as string,
    );
    const result = provider.value();
    expect(result).to.be.undefined();
    sinon.assert.notCalled(metadataStub);
  });

  it('returns undefined if metadata is undefined', () => {
    metadataStub.returns(undefined);
    const provider = new FileMetadataProvider(TestController, 'someMethod');
    const result = provider.value();
    expect(result).to.be.undefined();
    sinon.assert.calledOnceWithExactly(
      metadataStub,
      FileUtilBindings.FILE_REQUEST_METADATA,
      TestController.prototype,
      'someMethod',
    );
  });

  it('returns single file metadata when valid metadata is provided', () => {
    const mockMetadata: IFileRequestMetadata[] = [
      {fieldname: 'file', required: true} as unknown as Object,
    ];
    metadataStub.returns(mockMetadata);

    const provider = new FileMetadataProvider(TestController, 'uploadFile');
    const result = provider.value();

    expect(result).to.eql(mockMetadata[0]);
  });

  it('filters out undefined metadata values', () => {
    const mockMetadata: (IFileRequestMetadata | undefined)[] = [
      undefined,
      {fieldname: 'validFile', required: false} as unknown as Object,
    ];
    metadataStub.returns(mockMetadata as IFileRequestMetadata[]);

    const provider = new FileMetadataProvider(TestController, 'uploadFile');
    const result = provider.value();

    expect(result).to.eql({fieldname: 'validFile', required: false});
  });

  it('throws error if multiple valid file decorators are provided', () => {
    const mockMetadata: IFileRequestMetadata[] = [
      {fieldname: 'file1', required: true} as unknown as Object,
      {fieldname: 'file2', required: false} as unknown as Object,
    ];
    metadataStub.returns(mockMetadata);

    const provider = new FileMetadataProvider(TestController, 'uploadFile');
    expect(() => provider.value()).to.throw(
      'Multiple file decorators not supported',
    );
  });
});

describe('getFileMetadata (unit)', () => {
  let metadataStub: sinon.SinonStub;

  class TestController {
    // Dummy method to avoid empty class SonarQube issue
    dummyMethod(): void {
      // no-op
    }
  }

  beforeEach(() => {
    metadataStub = sinon.stub(MetadataInspector, 'getMethodMetadata');
  });

  afterEach(() => {
    sinon.restore();
  });

  it('calls MetadataInspector.getMethodMetadata with correct arguments', () => {
    const fakeMetadata: IFileRequestMetadata[] = [
      {fieldname: 'file', required: true} as unknown as Object,
    ];
    metadataStub.returns(fakeMetadata);

    const result = getFileMetadata(TestController, 'uploadFile');
    expect(result).to.eql(fakeMetadata);

    sinon.assert.calledOnceWithExactly(
      metadataStub,
      FileUtilBindings.FILE_REQUEST_METADATA,
      TestController.prototype,
      'uploadFile',
    );
  });

  it('returns undefined when MetadataInspector returns undefined', () => {
    metadataStub.returns(undefined);
    const result = getFileMetadata(TestController, 'uploadFile');
    expect(result).to.be.undefined();
  });
});
