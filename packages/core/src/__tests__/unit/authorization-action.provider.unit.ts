import {expect} from '@loopback/testlab';
import {AuthorizationMetadata} from 'loopback4-authorization';
import {Context} from '@loopback/core';
import {AuthorizeActionProvider} from '../../providers';
import {PermissionKey} from '../../permissions';
import {Request} from 'express';

describe('AuthorizeActionProvider', () => {
  const alwaysAllowedPaths = ['/health', '/ping'];

  const mockMetadata: AuthorizationMetadata = {
    permissions: ['ViewTenant', 'CreateTenant'],
  };

  let requestContext: Context;

  beforeEach(() => {
    requestContext = new Context();
  });

  it('allows access for admin users', async () => {
    const getMetadata = async () => mockMetadata;
    const provider = new AuthorizeActionProvider(
      async () => getMetadata(),
      alwaysAllowedPaths,
      requestContext,
    );

    const authorizeFn = provider.value();

    const mockRequest: Pick<Request, 'path'> = {path: '/restricted'};

    const result = await authorizeFn(
      [PermissionKey.Admin],
      mockRequest as unknown as Request,
    );
    expect(result).to.be.true();
  });

  it('allows access for always allowed paths', async () => {
    const getMetadata = async () => mockMetadata;
    const provider = new AuthorizeActionProvider(
      async () => getMetadata(),
      alwaysAllowedPaths,
      requestContext,
    );

    const authorizeFn = provider.value();

    const mockRequest: Pick<Request, 'path'> = {path: '/ping'};

    const result = await authorizeFn([], mockRequest as unknown as Request);
    expect(result).to.be.true();
  });

  it('allows access if permissions contain "*"', async () => {
    const getMetadata = async () =>
      ({
        permissions: ['*'],
      }) as AuthorizationMetadata;

    const provider = new AuthorizeActionProvider(
      async () => getMetadata(),
      alwaysAllowedPaths,
      requestContext,
    );

    const authorizeFn = provider.value();

    const mockRequest: Pick<Request, 'path'> = {path: '/somepath'};

    const result = await authorizeFn([], mockRequest as unknown as Request);
    expect(result).to.be.true();
  });

  it('throws error when no metadata and controller method does not exist', async () => {
    const getMetadata = async (): Promise<AuthorizationMetadata> => {
      throw new Error('API not found');
    };

    const provider = new AuthorizeActionProvider(
      async () => getMetadata(),
      alwaysAllowedPaths,
      requestContext,
    );

    const authorizeFn = provider.value();

    const mockRequest: Pick<Request, 'path'> = {path: '/notfound'};

    await expect(
      authorizeFn([], mockRequest as unknown as Request),
    ).to.be.rejectedWith(/API not found/);
  });

  it('denies access when user lacks required permissions', async () => {
    const getMetadata = async () => mockMetadata;

    const provider = new AuthorizeActionProvider(
      async () => getMetadata(),
      alwaysAllowedPaths,
      requestContext,
    );

    const authorizeFn = provider.value();

    const mockRequest: Pick<Request, 'path'> = {path: '/somepath'};

    const result = await authorizeFn(
      ['OtherPermission'],
      mockRequest as unknown as Request,
    );
    expect(result).to.be.false();
  });

  it('allows access when user has one of the required permissions', async () => {
    const getMetadata = async () => mockMetadata;

    const provider = new AuthorizeActionProvider(
      async () => getMetadata(),
      alwaysAllowedPaths,
      requestContext,
    );

    const authorizeFn = provider.value();

    const mockRequest: Pick<Request, 'path'> = {path: '/somepath'};

    const result = await authorizeFn(
      ['ViewTenant'],
      mockRequest as unknown as Request,
    );
    expect(result).to.be.true();
  });
});
