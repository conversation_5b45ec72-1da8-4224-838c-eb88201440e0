import {Model, model, property} from '@loopback/repository';
import {RecurringDto} from '../../types';

/**
 * Data transfer object (DTO) representing a product price.
 *
 * @remarks
 * - Contains pricing details such as currency, amount, and recurring configuration.
 * - Can be associated with a product in the billing system.
 */
@model()
export class PriceDto extends Model {
  /**
   * Unique identifier for the price object.
   */
  @property({
    type: 'string',
    description: 'Unique identifier for the object.',
  })
  id?: string;

  /**
   * Indicates whether the price is active and can be used for new purchases.
   */
  @property({
    type: 'boolean',
    description: 'Whether the price can be used for new purchases.',
  })
  active?: boolean;

  /**
   * Three-letter ISO currency code (lowercase).
   *
   * @example 'usd', 'eur', 'inr', 'gbp'
   */
  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      enum: ['usd', 'eur', 'inr', 'gbp'], // extend as per your supported currencies
    },
    description:
      'Three-letter ISO currency code, in lowercase. Must be a supported currency.',
  })
  currency: string;

  /**
   * Optional key-value metadata for storing additional structured information.
   */
  @property({
    type: 'object',
    required: false,
    description:
      'Set of key-value pairs that you can attach to an object for storing additional structured information.',
  })
  metadata?: {[key: string]: unknown};

  /**
   * The ID of the product this price is associated with.
   */
  @property({
    type: 'string',
    required: true,
    description: 'The ID of the product this price is associated with.',
  })
  product: string;

  /**
   * Recurring billing configuration for the price (e.g., interval, usage type).
   */
  @property({
    type: 'object',
    required: false,
    description:
      'The recurring components of a price such as interval and usage_type.',
  })
  recurring?: RecurringDto;

  /**
   * The unit amount in cents to be charged.
   *
   * @remarks
   * - Can be nullable depending on billing scheme.
   */
  @property({
    type: 'number',
    required: false,
    description:
      'The unit amount in cents to be charged. Nullable depending on billing scheme.',
  })
  //eslint-disable-next-line @typescript-eslint/naming-convention
  unit_amount?: number; // NOSONAR

  /**
   * Creates an instance of PriceDto.
   *
   * @param data - Partial data to initialize the DTO
   */
  constructor(data?: Partial<PriceDto>) {
    super(data);
  }
}
