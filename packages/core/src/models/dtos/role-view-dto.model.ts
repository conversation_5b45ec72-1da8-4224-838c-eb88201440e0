import {DataObject, Model, model, property} from '@loopback/repository';
import {BaseEntity} from '@sourceloop/core';

@model({
  name: 'v_roles',
  description: 'User details view in DB',
})
export class RoleView<T = DataObject<Model>> extends BaseEntity<T> {
  @property({
    type: 'string',
    name: 'role_id',
    id: true,
    required: true,
  })
  roleId: string;

  @property({
    name: 'role_name',
    type: 'string',
  })
  roleName?: string;

  @property({
    name: 'status',
    type: 'number',
  })
  status?: number;

  @property({
    type: 'string',
    required: true,
    name: 'tenant_id',
  })
  tenantId: string;

  @property({
    name: 'user_count',
    type: 'number',
    required: true,
  })
  userCount: number;
}
