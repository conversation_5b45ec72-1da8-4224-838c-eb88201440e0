import {model, Model, property} from '@loopback/repository';
@model({
  description:
    'model describing payload used to send forget password email for a tenant',
})
export class UpdatePasswordDto extends Model {
  @property({type: 'string', required: true, name: 'new_password'})
  newPassword: string;
  @property({type: 'string', required: true, name: 'client_secret'})
  clientSecret: string;
  @property({type: 'string', required: true, name: 'client_id'})
  clientId: string;

  constructor(data?: Partial<UpdatePasswordDto>) {
    super(data);
  }
}
