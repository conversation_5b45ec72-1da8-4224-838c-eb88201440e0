import {model, Model, property} from '@loopback/repository';
@model({
  description:
    'model describing payload used to send forget password email for a tenant',
})
export class ForgetPasswordRequestDto extends Model {
  @property({type: 'string', required: true})
  email: string;
  @property({type: 'string', required: true, name: 'client_secret'})
  // eslint-disable-next-line @typescript-eslint/naming-convention
  client_secret: string; //NO SONAR
  @property({type: 'string', required: true, name: 'client_id'})
  // eslint-disable-next-line @typescript-eslint/naming-convention
  client_id: string; //NO SONAR

  constructor(data?: Partial<ForgetPasswordRequestDto>) {
    super(data);
  }
}
