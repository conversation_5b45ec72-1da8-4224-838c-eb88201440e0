import {model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';

/**
 * Represents a template used for sending notifications such as email, SMS, or push.
 */
@model({
  name: 'notification_templates',
})
export class NotificationTemplates extends UserModifiableEntity {
  /**
   * Unique identifier for the notification template.
   */
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  /**
   * The name of the event associated with the notification.
   * Example: 'forget_password', 'otp_send'.
   */
  @property({
    name: 'event_name',
    type: 'string',
    required: true,
  })
  eventName: string;

  /**
   * The body/content of the notification message.
   */
  @property({
    type: 'string',
    required: true,
  })
  body: string;

  /**
   * Type of the notification (e.g., SMS, Email, Push).
   * This corresponds to the {@link NotificationType} enum.
   */
  @property({
    name: 'notification_type',
    type: 'number',
    required: true,
  })
  notificationType: number;

  /**
   * The subject of the notification. Used mainly for email-type notifications.
   */
  @property({
    type: 'string',
  })
  subject: string;

  /**
   * Constructs a new NotificationTemplates instance.
   * @param data Partial data to initialize the template.
   */
  constructor(data?: Partial<NotificationTemplates>) {
    super(data);
  }
}

/**
 * Interface representing the relations of NotificationTemplates model.
 */
export interface NotificationTemplatesRelations {}

/**
 * Type alias for a NotificationTemplates instance with relations loaded.
 */
export type NotificationTemplatesWithRelations = NotificationTemplates;
