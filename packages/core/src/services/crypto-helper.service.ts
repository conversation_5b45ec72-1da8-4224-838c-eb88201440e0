import {sign} from 'jsonwebtoken';
import {BindingScope, injectable} from '@loopback/core';
import {randomBytes} from 'crypto';
const FIVE_SECONDS = 5000;
@injectable({scope: BindingScope.SINGLETON})
export class CryptoHelperService {
  /**
   * The function generates a temporary token using a payload and an optional expiry time.
   * @param {T} payload - The `payload` parameter is an object that contains the data you want to include
   * in the token. This data can be any valid JSON object.
   * @param {number} [expiry] - The `expiry` parameter is an optional parameter that specifies the
   * expiration time for the generated token. It is a number that represents the duration in seconds
   * after which the token will expire. If no value is provided for `expiry`, the token will expire after
   * 5 seconds.
   * @returns a signed JWT token.
   */
  generateTempToken<T extends object>(payload: T, expiry?: number) {
    const secret = process.env.JWT_SECRET;
    if (!secret) {
      throw new Error(
        'JWT_SECRET is not defined in the environment variables.',
      );
    }
    //sonarignore:start
    return sign(payload, secret, {
      //sonarignore:end
      issuer: process.env.JWT_ISSUER,
      algorithm: 'HS256',
      expiresIn: expiry ?? FIVE_SECONDS,
    });
  }

  /**
   * The function generates a random string of a specified length using random bytes.
   * @param {number} length - The length parameter is a number that specifies the desired length of the
   * random string to be generated.
   * @returns a randomly generated string of hexadecimal characters.
   */
  generateRandomString(length: number) {
    // divided by two as the result is twice the length given
    return randomBytes(length / 2).toString('hex');
  }
}
