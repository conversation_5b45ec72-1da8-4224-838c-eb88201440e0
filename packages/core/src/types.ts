//eslint-disable-next-line @typescript-eslint/naming-convention
import <PERSON><PERSON> from 'stripe';
import {InvoiceStatus} from './enums';

export enum StripeEvents {
  INVOICE_PAYMENT_SUCCEEDED = 'invoice.payment_succeeded',
  CHARGE_SUCCEEDED = 'charge.succeeded',
  INVOICE_CREATED = 'invoice.created',
  INVOICE_UPDATED = 'invoice.updated',
  INVOICE_SENT = 'invoice.sent',
  INVOICE_OVERDUE = 'invoice.overdue',
  INVOICE_PAID = 'invoice.paid',
  SUBSCRIPTION_UPDATE = 'customer.subscription.updated',
}

export type StripeEventResponse<T = unknown> = {
  event: StripeEvents;
  success: boolean; // Whether the processing was successful
  data?: T; // Generic type to hold event-specific data

  sendEmail?: boolean;
  message?: string; // Optional message for additional context
  email?: string;
  userId?: string; // Optional user ID for context
  isProvisionRequired?: boolean; // Optional flag for provisioning requirements
};

export type StripeEventPayload =
  | PaymentSucceededEvent
  | ChargeSucceededEvent
  | InvoiceCreateEvent
  | InvoiceUpdateEvent
  | InvoiceSendEvent
  | InvoiceOverdueEvent
  | InvoicePaidEvent
  | SubscriptionUpdateEvent;
export type SubscriptionUpdateEvent = {
  type: StripeEvents.SUBSCRIPTION_UPDATE;
  data: Stripe.CustomerSubscriptionUpdatedEvent.Data;
};

export type InvoicePaidEvent = {
  type: StripeEvents.INVOICE_PAID;
  data: Stripe.InvoicePaidEvent.Data;
};

export type PaymentSucceededEvent = {
  type: StripeEvents.INVOICE_PAYMENT_SUCCEEDED;
  data: Stripe.InvoicePaymentSucceededEvent.Data;
};

export type ChargeSucceededEvent = {
  type: StripeEvents.CHARGE_SUCCEEDED;
  data: Stripe.ChargeSucceededEvent.Data;
};

export type InvoiceCreateEvent = {
  type: StripeEvents.INVOICE_CREATED;
  data: Stripe.InvoiceCreatedEvent.Data;
};

export type InvoiceUpdateEvent = {
  type: StripeEvents.INVOICE_UPDATED;
  data: Stripe.InvoiceUpdatedEvent.Data;
};

export type InvoiceSendEvent = {
  type: StripeEvents.INVOICE_SENT;
  data: Stripe.InvoiceSentEvent.Data;
};

export type InvoiceOverdueEvent = {
  type: StripeEvents.INVOICE_OVERDUE;
  data: Stripe.InvoiceOverdueEvent.Data;
};

export interface InvoiceResponse {
  status: InvoiceStatus;
  invoiceId: string;
  billingCustomerId: string;
  tenantId: string;
  isProvisionRequired?: boolean;
  subscriptionId?: string;
}

/**
 * Interface defining the component's options object
 */
export interface CoreComponentOptions {
  // Add the definitions here
}

/**
 * Default options for the component
 */
export const DEFAULT_CORE_OPTIONS: CoreComponentOptions = {
  // Specify the values here
};

export interface RecurringDto {
  //eslint-disable-next-line @typescript-eslint/naming-convention
  aggregate_usage?: string;
  interval: string;
  //eslint-disable-next-line @typescript-eslint/naming-convention
  interval_count?: number; // >= 1
  meter?: string;
  //eslint-disable-next-line @typescript-eslint/naming-convention
  usage_type?: 'metered' | 'licensed';
}
