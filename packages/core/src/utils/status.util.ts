export function getEnumMap<T extends Record<string, string | number>>(
  enumObj: T,
): {[key: number]: string} {
  return Object.keys(enumObj)
    .filter(key => !isNaN(Number(key))) // keep only numeric keys
    .reduce(
      (acc, key) => {
        const numKey = Number(key);
        // enumObj[numKey] will be string | number, so cast to string safely
        acc[numKey] = String(enumObj[numKey]);
        return acc;
      },
      {} as {[key: number]: string},
    );
}

export function numericEnumValues(enumType: Object) {
  return Object.keys(enumType)
    .map(key => Number(key))
    .filter(value => !isNaN(value));
}
