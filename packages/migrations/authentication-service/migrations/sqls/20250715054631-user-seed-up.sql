/* Set search path */
SET search_path TO main, public;

/* Insert single auth client */
INSERT INTO auth_clients (client_id, client_secret, secret)
VALUES ('temp_client', 'temp_secret', 'secret');

/* Insert single tenant */
INSERT INTO tenants (name, status, key)
VALUES ('Master Tenant', 1, 't1');

/* Insert single role for that tenant */
INSERT INTO roles (name, permissions, allowed_clients, role_type, tenant_id)
SELECT 'SuperAdmin', '{*}', '{temp_client}', 0, id
FROM tenants
WHERE key = 't1';

/* Insert single user */
-- Password = Dummy@123 (hashed already)
INSERT INTO users (first_name, last_name, username, email, default_tenant_id)
SELECT 'John', 'Doe', '<EMAIL>', '<EMAIL>', id
FROM tenants
WHERE key = 't1';

/* Assign user to tenant with the Admin role */
INSERT INTO user_tenants (user_id, tenant_id, status, role_id)
SELECT 
    (SELECT id FROM users WHERE username = '<EMAIL>'),
    (SELECT id FROM tenants WHERE key = 't1'),
    1,
    (SELECT id FROM roles WHERE role_type = 0 AND tenant_id = (SELECT id FROM tenants WHERE key = 't1') LIMIT 1);

/* Insert user credentials */
INSERT INTO user_credentials (user_id, auth_provider, password)
SELECT id, 'internal', '$2b$12$mNyM260paivMGoA0gThnkuYpBZ5V0yJHausASJtHINpMeUd9BJkwi'
FROM users
WHERE username = '<EMAIL>';

/* Update user with auth_client_ids */
UPDATE users
SET auth_client_ids = ARRAY[
    (SELECT id FROM auth_clients WHERE client_id = 'temp_client')::integer
]
WHERE username = '<EMAIL>';
