INSERT INTO main.currencies (currency_code, currency_name, symbol, country, created_by) VALUES
('USD', 'United States Dollar', '$', 'United States', '123e4567-e89b-12d3-a456-426614174000');

-- Insert MONTHLY billing cycle
INSERT INTO main.billing_cycles (cycle_name, duration, duration_unit, description, created_by)
VALUES ('MONTHLY', 1, 'month', 'Billing occurs every month.', '123e4567-e89b-12d3-a456-426614174002');

-- Insert QUARTERLY billing cycle
INSERT INTO main.billing_cycles (cycle_name, duration, duration_unit, description, created_by)
VALUES ('QUARTERLY', 3, 'month', 'Billing occurs every quarter.', '123e4567-e89b-12d3-a456-426614174002');

-- Insert ANNUALLY billing cycle
INSERT INTO main.billing_cycles (cycle_name, duration, duration_unit, description, created_by)
VALUES ('ANNUALLY', 12, 'month', 'Billing occurs every year.', '123e4567-e89b-12d3-a456-426614174002');