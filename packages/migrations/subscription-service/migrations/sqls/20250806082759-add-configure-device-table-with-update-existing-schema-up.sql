/* Replace with your SQL commands */

 CREATE  TABLE main.configure_devices ( 
	id                   uuid DEFAULT (md5(((random())::text || (clock_timestamp())::text)))::uuid NOT NULL  ,
	min                 numeric  NOT NULL  ,
	max				 numeric  NOT NULL  ,
	compute_size     text,
	db_size          text,
	created_on           timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL  ,
	modified_on          timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL  ,
	deleted              boolean DEFAULT false NOT NULL  ,
    deleted_on           timestamptz    ,
	deleted_by           uuid    ,
	created_by           uuid  NOT NULL  ,
	modified_by          uuid    ,
	CONSTRAINT pk_configure_devices_id PRIMARY KEY ( id )
 );

 CREATE  TABLE main.plan_history ( 
	id                   uuid DEFAULT (md5(((random())::text || (clock_timestamp())::text)))::uuid NOT NULL  ,
    price                numeric(10,2)    ,
    version             text NOT NULL,
	created_on           timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL  ,
	modified_on          timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL  ,
	deleted              boolean DEFAULT false NOT NULL  ,
    deleted_on           timestamptz    ,
	deleted_by           uuid    ,
	created_by           uuid  NOT NULL  ,
	modified_by          uuid    ,
    plan_id              uuid  NOT NULL ,
	CONSTRAINT pk_plan_history_id PRIMARY KEY ( id ),
    CONSTRAINT fk_plan_history_plans FOREIGN KEY ( plan_id ) REFERENCES main.plans( id )
 );
-- Step 1: Add the new columns
ALTER TABLE main.plans
ADD COLUMN configure_devices_id uuid NOT NULL,
ADD COLUMN plan_size_id uuid NOT NULL,
ADD COLUMN status numeric NOT NULL,
ADD COLUMN version text NOT NULL,
ADD COLUMN allowed_unlimited_users boolean,
ADD COLUMN cost_per_user numeric(10, 2);



ALTER TABLE main.subscriptions
ADD COLUMN number_of_users text;

-- Step 2: Add foreign key constraints
ALTER TABLE main.plans
ADD CONSTRAINT fk_plans_configure_devices
    FOREIGN KEY (configure_devices_id)
    REFERENCES main.configure_devices (id),

ADD CONSTRAINT fk_plans_plan_sizes
    FOREIGN KEY (plan_size_id)
    REFERENCES main.plan_sizes (id);

	ALTER TABLE main.plans
ADD CONSTRAINT chk_plan_name_valid
CHECK (
  name ~ '^[A-Za-z0-9 ]{3,50}$'
);

ALTER TABLE main.plans
ADD CONSTRAINT chk_plan_price_valid
CHECK (
  price > 0 AND price = round(price::numeric, 2)
);

ALTER TABLE main.plans
ADD CONSTRAINT unique_plan_combination UNIQUE (
    name,
    configure_devices_id,
    tier,
    billing_cycle_id
);

