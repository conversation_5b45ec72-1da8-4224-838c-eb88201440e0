
INSERT INTO main.plan_sizes (size, config, created_on, modified_on, deleted, created_by)
VALUES 
  (
    'SMALL', 
    jsonb_build_object(
      'ec2_instance_type', 't3.small',
      'rds_storage_type', 'gp2'
    ),
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    false,
    '123e4567-e89b-12d3-a456-************'  -- Replace with actual user UUID
  ),
  (
    'MEDIUM', 
    jsonb_build_object(
      'ec2_instance_type', 't3.medium',
      'rds_storage_type', 'gp3'
    ),
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    false,
    '123e4567-e89b-12d3-a456-************'
  ),
  (
    'LARGE', 
    jsonb_build_object(
      'ec2_instance_type', 't3.large',
      'rds_storage_type', 'io1'
    ),
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    false,
    '123e4567-e89b-12d3-a456-************'
  )
ON CONFLICT (size) DO NOTHING;

-- Seeder for main.configure_devices

INSERT INTO main.configure_devices (
  min, max, created_on, modified_on, deleted, created_by, db_size
)
VALUES 
  (
    1, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, false,
    '123e4567-e89b-12d3-a456-************', 't3.medium'  -- Replace with actual user ID
  ),
  (
    5, 8, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, false,
    '123e4567-e89b-12d3-a456-************', 't3.medium'  -- Replace with actual user ID
  ),
  (
    9, 16, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, false,
    '123e4567-e89b-12d3-a456-************', 't3.medium'  -- Replace with actual user ID
  );



INSERT INTO main.invoice (
    invoice_id,
    invoice_status,
    billing_customer_id,
    created_on,
    modified_on,
    deleted,
    deleted_on,
    deleted_by,
    created_by,
    modified_by
) VALUES
(
    'INV-1001',        -- invoice_id
    'PAID',            -- invoice_status
    '123e4567-e89b-12d3-a456-************', -- existing billing_customer_id
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    false,
    NULL,
    NULL,
    '123e4567-e89b-12d3-a456-************', -- created_by
    NULL
);