'use strict';

let dbm;
let type;
let seed;
let DbmPromise; // Custom promise from dbmigrate
const fs = require('fs');
const path = require('path');

/**
  * We receive the dbmigrate dependency from dbmigrate initially.
  * This enables us to not have to rely on NODE_PATH.
  */
exports.setup = function(options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
  DbmPromise = options.Promise;
};

exports.up = function(db) {
  const filePath = path.join(__dirname, 'sqls', '20250722043650-update-tenant-contact-models-up.sql');
  return new DbmPromise((resolve, reject) => {
    fs.readFile(filePath, {encoding: 'utf-8'}, (err, data) => {
      if (err) return reject(err);
      console.log('received data: ' + data);
      resolve(data);
    });
  }).then(data => {
    return db.runSql(data);
  });
};

exports.down = function(db) {
  const filePath = path.join(__dirname, 'sqls', '20250722043650-update-tenant-contact-models-down.sql');
  return new DbmPromise((resolve, reject) => {
    fs.readFile(filePath, {encoding: 'utf-8'}, (err, data) => {
      if (err) return reject(err);
      console.log('received data: ' + data);
      resolve(data);
    });
  }).then(data => {
    return db.runSql(data);
  });
};

exports._meta = {
  version: 1
};
