SET search_path TO main, public;

GRANT ALL ON SCHEMA main TO public;

ALTER TABLE main.tenants
    ADD COLUMN lang VARCHAR(255);

ALTER TABLE main.contacts
    ADD COLUMN designation VARCHAR(255),
    ADD COLUMN phone_number VARCHAR(100),
    ADD COLUMN country_code VARCHAR(10);


CREATE TABLE main.files (
    id uuid DEFAULT (md5(((random())::text ||(clock_timestamp())::text))) ::uuid NOT NULL,
    source smallint NOT NULL,
    original_name varchar(255) NOT NULL,
    file_key varchar(255) NOT NULL,
    size BIGINT  DEFAULT 0,
    created_on timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on timestamptz,
    created_by uuid,
    modified_by uuid,
    deleted boolean DEFAULT FALSE NOT NULL,
    deleted_by uuid,
    deleted_on timestamptz,
    tenant_id uuid NOT NULL,
    CONSTRAINT pk_files PRIMARY KEY (id),
    CONSTRAINT fk_files_tenants FOREIGN KEY (tenant_id) REFERENCES main.tenants(id)
);

COMMENT ON COLUMN main.files.source IS 'source of a files, it can be - 0(S3) and so on';