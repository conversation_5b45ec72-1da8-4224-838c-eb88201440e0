/* Replace with your SQL commands */
CREATE TABLE main.notification_templates(
    id uuid DEFAULT (md5(((random())::text ||(clock_timestamp())::text))) ::uuid NOT NULL,
    event_name varchar(500) NOT NULL,
    body text NOT NULL,
    notification_type numeric NOT NULL,
    subject varchar(200),
    created_by uuid,
    created_on timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_by uuid,
    modified_on timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted bool DEFAULT FALSE NOT NULL,
    deleted_on timestamptz,
    deleted_by uuid,
    CONSTRAINT pk_email_templates_id PRIMARY KEY (id)
);


INSERT INTO main.notification_templates(event_name, body, subject, notification_type)
    VALUES ('forget_password',
    '<!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
      <title>Password Reset</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          background-color: #f7f7f7;
          margin: 0;
          padding: 0;
        }
        .container {
          max-width: 600px;
          margin: 40px auto;
          background: #ffffff;
          padding: 30px;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          text-align: center;
        }
        h2 {
          color: #333333;
        }
        p {
          font-size: 16px;
          color: #555555;
        }
        .reset-button {
          display: inline-block;
          margin-top: 20px;
          background-color: #007BFF;
          color: #ffffff;
          padding: 12px 20px;
          text-decoration: none;
          font-size: 16px;
          border-radius: 5px;
        }
        .note {
          font-size: 14px;
          color: #999999;
          margin-top: 15px;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <h2>Password Reset Request</h2>
        <p>We received a request to reset your password. Click the button below to set a new password:</p>

        <a href="{{{RESET_LINK}}}" class="reset-button">Reset Password</a>

       </div>
    </body>
    </html>
  ','Reset Password Link',1);



INSERT INTO main.notification_templates(event_name, body, subject, notification_type)
    VALUES ('otp_send',
    '<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OTP Verification</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #f7f7f7;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 40px auto;
      background: #ffffff;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      text-align: center;
    }
    h2 {
      color: #333333;
    }
    p {
      font-size: 16px;
      color: #555555;
    }
    .otp-box {
      display: inline-block;
      margin: 20px 0;
      font-size: 28px;
      font-weight: bold;
      letter-spacing: 4px;
      color: #2c3e50;
      background: #f0f0f0;
      padding: 12px 24px;
      border-radius: 6px;
    }
    .note {
      font-size: 14px;
      color: #999999;
      margin-top: 15px;
    }
    .footer {
      margin-top: 30px;
      font-size: 12px;
      color: #aaaaaa;
    }
  </style>
</head>
<body>
  <div class="container">
    <h2>OTP Verification</h2>
    <p>Use the following One-Time Password (OTP) to complete your verification process:</p>

    <div class="otp-box">{{OTP}}</div>

    <p class="note">This OTP is valid for {{expiry}} minutes. Do not share it with anyone.</p>

  </div>
</body>
</html>

  ','Verify Email',1);

