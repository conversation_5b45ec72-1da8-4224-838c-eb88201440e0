{"version": "2.0.0", "tasks": [{"label": "AuthenticationService", "type": "shell", "command": "npm start", "options": {"cwd": "${workspaceFolder}/services/authentication-service"}, "isBackground": false, "presentation": {"reveal": "always", "panel": "new"}}, {"label": "SubscriptionService", "type": "shell", "command": "npm start", "options": {"cwd": "${workspaceFolder}/services/subscription-service"}, "isBackground": false, "presentation": {"reveal": "always", "panel": "new"}}, {"label": "NotificationService", "type": "shell", "command": "npm start", "options": {"cwd": "${workspaceFolder}/services/notification-service"}, "isBackground": false, "presentation": {"reveal": "always", "panel": "new"}}, {"label": "TenantManagementService", "type": "shell", "command": "npm start", "options": {"cwd": "${workspaceFolder}/services/tenant-mgmt-service"}, "isBackground": false, "presentation": {"reveal": "always", "panel": "new"}}, {"label": "UserTenantManagementService", "type": "shell", "command": "npm start", "options": {"cwd": "${workspaceFolder}/services/user-tenant-service"}, "isBackground": false, "presentation": {"reveal": "always", "panel": "new"}}, {"label": "AuthenticationFacade", "type": "shell", "command": "npm start", "options": {"cwd": "${workspaceFolder}/facades/authentication-facade"}, "isBackground": false, "presentation": {"reveal": "always", "panel": "new"}}, {"label": "TenantManagementFacade", "type": "shell", "command": "npm start", "options": {"cwd": "${workspaceFolder}/facades/tenant-mgmt-facade"}, "isBackground": false, "presentation": {"reveal": "always", "panel": "new"}}, {"label": "Start All Services", "dependsOn": ["AuthenticationService", "TenantManagementService", "UserTenantManagementService", "AuthenticationFacade", "TenantManagementFacade", "SubscriptionService", "NotificationService"], "dependsOrder": "parallel", "presentation": {"reveal": "always", "panel": "shared"}, "problemMatcher": ["$tsc-watch"], "isBackground": true}]}