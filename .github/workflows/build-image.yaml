# This is a basic workflow to help you get started with Actions

name: Build and Push Image

# Controls when the action will run. Triggers the workflow on push or pull request
# events but only for the master branch
on:
  push:
    branches: [master]

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  docker_build:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest
    env:
      IMAGE_REPO_NAME: ${{ secrets.DOCKERHUB_USERNAME }}
      NR_ENABLED: 0

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: '**/package-lock.json'

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: setup packages
        run: 'npm i'

      - name: lerna build
        run: 'npm run build --workspaces --if-present'

      - name: lerna test
        run: 'npm run test --workspaces --if-present'

      - name: lerna docker build
        run: 'npx lerna run docker:build --stream --concurrency 2'

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: lerna docker push
        run: 'npx lerna run docker:push'
