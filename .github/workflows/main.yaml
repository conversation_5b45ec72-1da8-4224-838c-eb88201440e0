name: CI

on:
  pull_request:
    branches: [dev]

# This workflow contains a single job called "npm_test"
jobs:
  npm_test:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20.x'

      - name: Install Monorepo Deps
        run: npm i

      - name: Run Test Cases
        run: npm run test --workspaces --if-present

      - name: Run Lint Checks
        run: npm run lint --workspaces --if-present
