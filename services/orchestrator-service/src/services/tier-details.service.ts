import {injectable, /* inject, */ BindingScope, Provider} from '@loopback/core';
import {TierDetailsFn} from '@sourceloop/ctrl-plane-orchestrator-service';
import {marshall, unmarshall} from '@aws-sdk/util-dynamodb';

import {DynamoDBClient, QueryCommand} from '@aws-sdk/client-dynamodb';

@injectable({scope: BindingScope.TRANSIENT})
export class TierDetailsProvider implements Provider<TierDetailsFn> {
  value() {
    // prettier-ignore
    return async (tier: string) => { // NOSONAR
      console.info("tier---------",tier);
      const restier = await this.fetchTierDetails(tier);
      console.info("TierDetails---------",restier);
      return this.fetchTierDetails(tier); // NOSONAR
    }; // NOSONAR
  }
  private async fetchTierDetails(tier: string) {
    const client = new DynamoDBClient({region: process.env.DYNAMO_DB_REGION});
    const params = {
      TableName: process.env.TIER_DETAILS_TABLE,
      KeyConditionExpression: 'tier = :tier',
      ExpressionAttributeValues: marshall({
        ':tier': tier,
      }),
    };

    try {
      const command = new QueryCommand(params);
      const response = await client.send(command);
      if (!response.Items) {
        throw Error('Items not found.'); // NOSONAR
      }
      const items = response.Items.map(item => unmarshall(item));
      console.info('Query results:', items); // NOSONAR

      if (items.length === 0) {
        throw new Error(
          'Provided tier details not found in tier mapping table.',
        );
      }

      // Extract tier details from the fetched items
      const tierDetails = items[0];
      return {...tierDetails, jobIdentifier: tierDetails.jobName};
    } catch (error) {
      console.error('Error fetching data:', error); // NOSONAR
      throw error;
    }
  }
}
