import {
  injectable,
  BindingScope,
  Provider,
  inject,
  service,
} from '@loopback/core';
import {AnyObject} from '@loopback/repository';
import {
  BuilderService,
  TenantProvisioningHandler,
  TierDetailsFn,
  OrchestratorServiceBindings,
} from '@sourceloop/ctrl-plane-orchestrator-service';
import {DataStoreService} from './data-store.service';

export interface ProvisioningInputs {
  planConfig: AnyObject;
  builderConfig: AnyObject;
  tenant: {
    id: string;
    identityProvider: string;
    name: string;
    status: number;
    key: string;
    spocUserId: string | null;
    domains: string[];
    leadId: string | null;
    addressId: string;
    contacts: {
      id: string;
      firstName: string;
      lastName: string;
      email: string;
      isPrimary: boolean;
      type: string | null;
      tenantId: string;
    }[];
    address: {
      id: string;
      address: string;
      city: string | null;
      state: string | null;
      zip: string;
      country: string;
    };
  };
}

@injectable({scope: BindingScope.TRANSIENT})
export class TenantProvisioningHandlerProvider
  implements Provider<TenantProvisioningHandler<ProvisioningInputs>>
{
  constructor(
    @inject(OrchestratorServiceBindings.TIER_DETAILS_PROVIDER)
    private tierDetails: TierDetailsFn,
    @inject(OrchestratorServiceBindings.BUILDER_SERVICE)
    private builderService: BuilderService,

    @service(DataStoreService)
    private readonly dataStoreService: DataStoreService,
  ) {}

  value() {
    return async (body: ProvisioningInputs) => {
      console.info('tenant provision body---------', body);

      // Extract plan and builder information from the body
      const planConfig = body.planConfig; // NOSONAR
      console.info('PlanConfig:', planConfig); // NOSONAR

      const builder = body.builderConfig; // NOSONAR
      console.info('BuilderConfig:', builder); // NOSONAR
      const tier = planConfig.tier; // NOSONAR
      console.info('Tier:', tier); // NOSONAR
      const tenant = body.tenant; // NOSONAR
      console.info('Tenant:', tenant); // NOSONAR
      console.info('step 1');
      if (planConfig?.features) {
        for (const feature of planConfig.features) {
          console.info('Feature:', feature.key); // NOSONAR
          if (feature.key === 'IdP') {
            console.info('inside if IDP'); // NOSONAR
            // prettier-ignore
            console.info( // NOSONAR
            
            'FeatureIDP:', // NOSONAR
            feature.key, // NOSONAR
            feature.value?.value, // NOSONAR
            feature.defaultValue, // NOSONAR
          ); // NOSONAR
            body.tenant.identityProvider =
              feature.value?.value ?? feature.defaultValue; //check featuresValue if overriden otherwise use default value
          }
        }
      }

      console.info('step 2');
      body.planConfig.features ??= [];
      builder.config.environmentOverride.tenant = JSON.stringify(body.tenant);

      console.info('Tenant:', body.tenant); // NOSONAR
      console.info('---------------'); // NOSONAR
      console.info('BuilderConfig:', builder); // NOSONAR
      await this.dataStoreService.storeDataInDynamoDB({
        tenantId: tenant.id,
        ...body,
      });

      console.info('step 3');
      try {
        // Fetch tier details based on the provided tier
        const {jobIdentifier, ...otherTierDetails} =
          await this.tierDetails(tier);
        const jobName = jobIdentifier;

        console.info('step 4');
        // Ensure Job name is present in the tier details
        if (!jobName) {
          throw new Error('Builder Job name not found in plan details');
        }

        // Check if the builder type is CODE_BUILD
        if (builder?.type === 'CODE_BUILD') {
          console.info('step 5');
          // Trigger CodeBuild with the necessary environments
          const codeBuildResponse = await this.builderService.startJob(
            jobName,
            {
              ...otherTierDetails,
              ...(builder?.config?.environmentOverride ?? {}),
            },
          );

          console.info('step 6');

          console.info('Code Build Response: ', codeBuildResponse); // NOSONAR

          return;
        } else {
          // Throw an error if the builder config is invalid
          throw Error('Invalid builder config provided.');
        }
      } catch (error) {
        console.error('Error in tenant provisioning:', error); // NOSONAR
        return;
      }
    };
  }
}
