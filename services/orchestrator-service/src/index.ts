import {ApplicationConfig, OrchestratorServiceApplication} from './application';
require('dotenv').config();
export * from './application';

export async function main(options: ApplicationConfig = {}) {
  const app = new OrchestratorServiceApplication(options);
  await app.boot();
  await app.start();

  const url = app.restServer.url;
  console.info(`Server is running at ${url}`); // NOSONAR
  console.info(`Try ${url}/ping`); // NOSONAR

  return app;
}

if (require.main === module) {
  // Run the application
  const config = {
    rest: {
      port: +(process.env.PORT ?? 3000), // NOSONAR
      host: process.env.HOST,
      // The `gracePeriodForClose` provides a graceful close for http/https
      // servers with keep-alive clients. The default value is `Infinity`
      // (don't force-close). If you want to immediately destroy all sockets
      // upon stop, set its value to `0`.
      // See https://www.npmjs.com/package/stoppable
      gracePeriodForClose: 5000, // 5 seconds // NOSONAR
      openApiSpec: {
        // useful when used with OpenAPI-to-GraphQL to locate your application
        setServersFromRequest: true,
      },
    },
  };
  main(config).catch(err => {
    console.error('Cannot start the application.', err); // NOSONAR
    process.exit(1);
  });
}
