import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  HttpErrors,
} from '@loopback/rest';
import {authorize} from 'loopback4-authorization';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {
  CONTENT_TYPE,
  OPERATION_SECURITY_SPEC,
  STATUS_CODE,
} from '@sourceloop/core';
import {PlanRepository} from '../repositories/plan.repository';
import {getEnumMap, PermissionKey, PlanStatus, StatusDto} from '@local/core';
import {Plan} from '../models/plan.model';
import {BillingComponentBindings} from 'loopback4-billing';
import {inject} from '@loopback/core';
import {IBillingService} from '../types';

const basePath = '/plans';

export class PlanController {
  constructor(
    @repository(PlanRepository)
    public planRepository: PlanRepository,
    @inject(BillingComponentBindings.SDKProvider)
    private readonly billingProvider: IBillingService,
  ) {}

  /**
   * Create a new Plan instance.
   *
   * @param plan - Plan data excluding 'id' and 'status'.
   * @returns The newly created Plan instance with status defaulted to ACTIVE.
   * @security Bearer token required, with `CreatePlan` permission.
   */
  @authorize({
    permissions: [PermissionKey.CreatePlan],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @post(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Plan model instance',
        content: {
          'application/json': {schema: getModelSchemaRef(Plan)},
        },
      },
    },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Plan, {
            title: 'NewPlan',
            exclude: ['id', 'status', 'productRefId'],
          }),
        },
      },
    })
    plan: Omit<Plan, 'id' | 'status' | 'productRefId'>,
  ): Promise<Plan> {
    try {
      const prodRefId = await this.billingProvider.createProduct({
        name: plan.name,
        description: plan.description,
      });

      return await this.planRepository.create({
        ...plan,
        status: PlanStatus.ACTIVE,
        productRefId: prodRefId,
      });
    } catch (error) {
      if (error.constraint === 'unique_plan_combination') {
        throw new HttpErrors.BadRequest(
          'A plan with the same name, device count, infra configuration, and subscription tenure already exists. Please modify one or more fields to create a unique plan.',
        );
      }
      throw error; // rethrow for anything else
    }
  }

  /**
   * Get the count of Plan instances matching the optional where filter.
   *
   * @param where - Optional filter to count matching Plans.
   * @returns Count of matching Plans.
   * @security Bearer token required, with `ViewPlan` permission.
   */
  @authorize({
    permissions: [PermissionKey.ViewPlan],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/count`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Plan model count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async count(@param.where(Plan) where?: Where<Plan>): Promise<Count> {
    return this.planRepository.count(where);
  }

  /**
   * Find Plan instances optionally filtered.
   *
   * @param filter - Optional filter to refine the find.
   * @returns Array of matching Plan instances.
   * @security Bearer token required, with `ViewPlan` permission.
   */
  @authorize({
    permissions: [PermissionKey.ViewPlan],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Plan model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(Plan, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(@param.filter(Plan) filter?: Filter<Plan>): Promise<Plan[]> {
    return this.planRepository.find(filter);
  }

  /**
   * Update multiple Plan instances matching the where filter.
   *
   * @param plan - Partial Plan data to update.
   * @param where - Optional where filter to select Plans to update.
   * @returns Count of updated Plan instances.
   * @security Bearer token required, with `UpdatePlan` permission.
   */
  @authorize({
    permissions: [PermissionKey.UpdatePlan],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @patch(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Plan PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Plan, {partial: true}),
        },
      },
    })
    plan: Plan,
    @param.where(Plan) where?: Where<Plan>,
  ): Promise<Count> {
    return this.planRepository.updateAll(plan, where);
  }

  /**
   * Find a Plan instance by its ID.
   *
   * @param id - Plan ID.
   * @param filter - Optional filter excluding where condition.
   * @returns The Plan instance if found.
   * @security Bearer token required, with `ViewPlan` permission.
   */
  @authorize({
    permissions: [PermissionKey.ViewPlan],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Plan model instance',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Plan, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Plan, {exclude: 'where'}) filter?: FilterExcludingWhere<Plan>,
  ): Promise<Plan> {
    return this.planRepository.findById(id, filter);
  }

  /**
   * Update a Plan instance by its ID.
   *
   * @param id - Plan ID.
   * @param plan - Partial Plan data to update.
   * @security Bearer token required, with `UpdatePlan` permission.
   */
  @authorize({
    permissions: [PermissionKey.UpdatePlan],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @patch(`${basePath}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Plan PATCH success',
      },
    },
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Plan, {partial: true}),
        },
      },
    })
    plan: Plan,
  ): Promise<void> {
    await this.planRepository.updateById(id, plan);
  }

  /**
   * Replace a Plan instance by its ID.
   *
   * @param id - Plan ID.
   * @param plan - Complete Plan data to replace with.
   * @security Bearer token required, with `UpdatePlan` permission.
   */
  @authorize({
    permissions: [PermissionKey.UpdatePlan],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @put(`${basePath}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Plan PUT success',
      },
    },
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() plan: Plan,
  ): Promise<void> {
    await this.planRepository.replaceById(id, plan);
  }

  /**
   * Delete a Plan instance by its ID.
   *
   * @param id - Plan ID.
   * @security Bearer token required, with `DeletePlan` permission.
   */
  @authorize({
    permissions: [PermissionKey.DeletePlan],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @del(`${basePath}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Plan DELETE success',
      },
    },
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.planRepository.deleteById(id);
  }

  /**
   * Retrieve all possible plan statuses as a mapping.
   *
   * @returns StatusDto object containing all plan statuses.
   * @security Bearer token required, with `ViewAllStatuses` permission.
   */
  @authorize({
    permissions: [PermissionKey.ViewAllStatuses],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/all-status`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Object of all possible tenant status',
        permissions: [PermissionKey.ViewAllStatuses],
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'object',
              items: getModelSchemaRef(StatusDto, {
                title: 'PlanStatusDto',
              }),
            },
          },
        },
      },
    },
  })
  async findAllStatus(): Promise<StatusDto> {
    const statusMap = getEnumMap(PlanStatus);
    return new StatusDto({statuses: statusMap});
  }
}
