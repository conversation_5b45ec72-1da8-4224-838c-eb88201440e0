import {PermissionKey, PriceDto} from '@local/core';
import {getModelSchemaRef, post, requestBody} from '@loopback/rest';
import {OPERATION_SECURITY_SPEC, STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {BillingComponentBindings} from 'loopback4-billing';
import {inject} from '@loopback/core';
import {IBillingService} from '../types';

const basePath = '/prices';

/**
 * Controller for handling price-related operations.
 */
export class PriceController {
  /**
   * Creates an instance of PriceController.
   * @param billingProvider - Billing service provider used for price operations.
   */
  constructor(
    @inject(BillingComponentBindings.SDKProvider)
    private readonly billingProvider: IBillingService,
  ) {}

  /**
   * Creates a new price.
   *
   * @param priceDto - The data transfer object for creating a price,
   * excluding the `id` field since it will be auto-generated.
   * @returns A promise that resolves to the created {@link PriceDto}.
   *
   * @throws {HttpErrors.Unauthorized} If the request is not authenticated.
   * @throws {HttpErrors.Forbidden} If the user does not have permission to create a price.
   * @throws {HttpErrors.UnprocessableEntity} If the request body is invalid.
   */
  @authorize({
    permissions: [PermissionKey.CreatePrice],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @post(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'price model instance',
        content: {
          'application/json': {schema: getModelSchemaRef(PriceDto)},
        },
      },
    },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PriceDto, {
            title: 'NewPrice',
            exclude: ['id'],
          }),
        },
      },
    })
    priceDto: Omit<PriceDto, 'id'>,
  ): Promise<PriceDto> {
    return this.billingProvider.createPrice(priceDto);
  }
}
