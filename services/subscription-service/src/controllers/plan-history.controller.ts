import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {PlanHistory} from '../models/plan-history.model';
import {PermissionKey} from '@local/core';
import {OPERATION_SECURITY_SPEC} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PlanHistoryRepository} from '../repositories';

const basePath = '/plan-histories';

/**
 * Controller for managing PlanHistory records related to Plans.
 */
export class PlanHistoryController {
  /**
   * Creates an instance of PlanHistoryController.
   * @param planHistoryRepository - Repository for PlanHistory model.
   */
  constructor(
    @repository(PlanHistoryRepository)
    protected planHistoryRepository: PlanHistoryRepository,
  ) {}

  /**
   * Retrieves an array of PlanHistory instances matching the optional filter.
   *
   * @param filter - Optional filter object to refine the search.
   * @returns Array of PlanHistory records.
   *
   * @authorization Requires permission: ViewPlanHistory
   * @authentication Bearer token required
   */
  @authorize({
    permissions: [PermissionKey.ViewPlanHistory],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'Array of Plan has many PlanHistory',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(PlanHistory)},
          },
        },
      },
    },
  })
  async find(
    @param.query.object('filter') filter?: Filter<PlanHistory>,
  ): Promise<PlanHistory[]> {
    return this.planHistoryRepository.find(filter);
  }

  /**
   * Creates a new PlanHistory record.
   *
   * @param planHistory - PlanHistory data excluding 'id'.
   * @returns The newly created PlanHistory instance.
   *
   * @authorization Requires permission: CreatePlanHistory
   * @authentication Bearer token required
   */
  @authorize({
    permissions: [PermissionKey.CreatePlanHistory],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @post(`${basePath}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'Plan model instance',
        content: {'application/json': {schema: getModelSchemaRef(PlanHistory)}},
      },
    },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PlanHistory, {
            title: 'NewPlanHistoryInPlan',
            exclude: ['id'],
            optional: ['planId'],
          }),
        },
      },
    })
    planHistory: Omit<PlanHistory, 'id'>,
  ): Promise<PlanHistory> {
    return this.planHistoryRepository.create(planHistory);
  }

  /**
   * Updates multiple PlanHistory instances matching the where filter.
   *
   * @param planHistory - Partial data to update matching PlanHistory records.
   * @param where - Optional where filter to specify which records to update.
   * @returns Count of records updated.
   *
   * @authorization Requires permission: UpdatePlanHistory
   * @authentication Bearer token required
   */
  @authorize({
    permissions: [PermissionKey.UpdatePlanHistory],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @patch(`${basePath}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'Plan.PlanHistory PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PlanHistory, {partial: true}),
        },
      },
    })
    planHistory: Partial<PlanHistory>,
    @param.query.object('where', getWhereSchemaFor(PlanHistory))
    where?: Where<PlanHistory>,
  ): Promise<Count> {
    return this.planHistoryRepository.updateAll(planHistory, where);
  }

  /**
   * Deletes a PlanHistory instance by its ID.
   *
   * @param id - ID of the PlanHistory to delete.
   *
   * @authorization Requires permission: DeletePlanHistory
   * @authentication Bearer token required
   */
  @authorize({
    permissions: [PermissionKey.DeletePlanHistory],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @del(`${basePath}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'Plan.PlanHistory DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.planHistoryRepository.deleteById(id);
  }
}
