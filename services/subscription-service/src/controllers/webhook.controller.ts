import {intercept, service} from '@loopback/core';
import {post, requestBody} from '@loopback/rest';
import {authorize} from 'loopback4-authorization';
import {WEBHOOK_VERIFIER} from '@sourceloop/ctrl-plane-subscription-service';
import {StripeWebhookService} from '../services';
import {
  InvoiceResponse,
  StripeEventPayload,
  StripeEventResponse,
} from '@local/core';

/**
 * Controller responsible for handling webhook requests from payment providers.
 */
export class WebhookController {
  /**
   * Creates an instance of WebhookController.
   * @param stripeWebhookService - Service to handle Stripe webhook events.
   */
  constructor(
    @service(StripeWebhookService)
    public stripeWebhookService: StripeWebhookService,
  ) {}

  /**
   * Handles incoming payment provider webhook events.
   *
   * @param payload - Raw webhook payload received from the payment provider.
   * @returns A promise that resolves to a StripeEventResponse containing
   *          invoice response data or undefined.
   *
   * @throws {HttpErrors.UnprocessableEntity} If the payload is invalid or cannot be processed.
   */
  @authorize({
    permissions: ['*'],
  })
  @intercept(WEBHOOK_VERIFIER)
  @post('/webhooks/payment')
  async handleWebhook(
    @requestBody({
      description: 'Payment Provider webhook payload',
      required: true,
      content: {
        'application/json': {
          'x-parser': 'raw',
          schema: {type: 'object'},
        },
      },
    })
    payload: StripeEventPayload,
  ): Promise<StripeEventResponse<InvoiceResponse | undefined>> {
    const jsonString = payload.toString();
    const parsedPayload = JSON.parse(jsonString);
    return this.stripeWebhookService.handleWebhookEvent(parsedPayload);
  }
}
