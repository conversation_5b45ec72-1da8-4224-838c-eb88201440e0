import {InvoiceStatus, PermissionKey, StatusDto} from '@local/core';
import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {get, getModelSchemaRef, param} from '@loopback/rest';
import {
  CONTENT_TYPE,
  OPERATION_SECURITY_SPEC,
  STATUS_CODE,
} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {TenantBillingsView} from '../models';
import {TenantBillingsViewRepository} from '../repositories';

const basePath = '/tenant-billings';

/**
 * Controller for handling tenant billing operations.
 *
 * Provides endpoints to:
 * - Retrieve a list of tenant billing records with optional filtering.
 * - Get the count of tenant billing records matching a filter.
 * - Retrieve all possible tenant billing statuses.
 *
 * Security:
 * - All endpoints require Bearer authentication.
 * - Permissions are enforced per endpoint using the `@authorize` decorator.
 *
 * Endpoints:
 * - `GET /tenant-billings`: List tenant billing records.
 * - `GET /tenant-billings/count`: Get count of tenant billing records.
 * - `GET /tenant-billings/all-status`: Get all possible tenant billing statuses.
 *
 * @remarks
 * Uses `TenantBillingsViewRepository` for data access.
 */
export class TenantBillingsController {
  constructor(
    @repository(TenantBillingsViewRepository)
    public tenantBillingsViewRepository: TenantBillingsViewRepository,
  ) {}

  @authorize({
    permissions: [PermissionKey.ViewTenantBillings],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of ConfigureDevice model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(TenantBillingsView, {
                includeRelations: true,
              }),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(TenantBillingsView) filter?: Filter<TenantBillingsView>,
  ): Promise<TenantBillingsView[]> {
    return this.tenantBillingsViewRepository.find(filter);
  }

  @authorize({
    permissions: [PermissionKey.ViewTenantBillings],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/count`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Plan model count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.where(TenantBillingsView) where?: Where<TenantBillingsView>,
  ): Promise<Count> {
    return this.tenantBillingsViewRepository.count(where);
  }

  @authorize({
    permissions: [PermissionKey.ViewAllStatuses],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/all-status`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Object of all possible tenant billing status',
        permissions: [PermissionKey.ViewAllStatuses],
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'object',
              items: getModelSchemaRef(StatusDto, {
                title: 'TenantBillingStatusDto',
              }),
            },
          },
        },
      },
    },
  })
  async findAllStatus(): Promise<StatusDto> {
    return new StatusDto({statuses: InvoiceStatus});
  }
}
