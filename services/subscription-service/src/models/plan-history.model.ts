import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {Plan} from './plan.model';

/**
 * Model representing the history of a Plan.
 *
 * @remarks
 * This entity tracks historical versions and pricing of a Plan. It extends
 * UserModifiableEntity, so it includes auditing fields like createdBy,
 * modifiedBy, createdAt, and modifiedAt.
 *
 * @property {string} id - Unique identifier for the plan history record (auto-generated).
 * @property {number} [price] - Historical price of the plan with precision of 10 and scale of 2.
 * @property {string} version - Version identifier for the plan history (required).
 * @property {string} planId - Foreign key referencing the related Plan's id.
 */
@model({
  name: 'plan_history',
})
export class PlanHistory extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id: string;

  @property({
    type: 'number',
    precision: 10,
    scale: 2,
  })
  price?: number;

  @property({
    type: 'string',
    required: true,
  })
  version: string;

  /**
   * Indicates if the plan allows unlimited users.
   */
  @property({
    type: 'boolean',
    description: 'Indicates if the plan allows unlimited users.',
    name: 'allowed_unlimited_users',
  })
  allowedUnlimitedUsers: boolean;

  /**
   * Price added per user for the plan, if applicable.
   */
  @property({
    type: 'number',
    description: 'price added per user for the plan.',
    name: 'cost_per_user',
  })
  costPerUser?: number;

  @belongsTo(
    () => Plan,
    {
      keyTo: 'id',
    },
    {
      name: 'plan_id',
    },
  )
  planId: string;

  constructor(data?: Partial<PlanHistory>) {
    super(data);
  }
}
