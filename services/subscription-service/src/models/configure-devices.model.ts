import {model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';

/**
 * Represents a ConfigureDevice entity with minimum and maximum configuration values.
 *
 * @remarks
 * This model extends UserModifiableEntity, so it includes auditing fields like
 * createdBy, modifiedBy, createdAt, and modifiedAt automatically.
 *
 * @property {string} id - Unique identifier (auto-generated).
 * @property {number} min - Minimum configuration value (required).
 * @property {number} max - Maximum configuration value (required).
 */
@model({
  name: 'configure_devices',
})
export class ConfigureDevice extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id: string;

  @property({
    type: 'number',
    required: true,
  })
  min: number;

  @property({
    type: 'number',
    required: true,
  })
  max: number;

  @property({
    type: 'string',
    name: 'compute_size',
  })
  computeSize?: string;

  @property({
    type: 'string',
    name: 'db_size',
  })
  dbSize?: string;

  constructor(data?: Partial<ConfigureDevice>) {
    super(data);
  }
}
