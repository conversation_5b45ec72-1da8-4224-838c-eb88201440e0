import {BillingCustomer} from './billing-customer.model';
import {InvoiceStatus} from '@local/core';
import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';

@model({
  name: 'invoice',
})
export class Invoice extends UserModifiableEntity {
  /**
   * Unique identifier of the invoice record in the database.
   */
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  /**
   * The unique identifier assigned by the billing system for the invoice.
   */
  @property({
    type: 'string',
    name: 'invoice_id',
    required: true,
  })
  invoiceId: string;

  /**
   * The current status of the invoice or its associated payment.
   */
  @property({
    type: 'string',
    name: 'invoice_status',
    description: 'payment or invoice status',
  })
  invoiceStatus?: InvoiceStatus;

  /**
   * The associated billing customer for this invoice.
   */
  @belongsTo(
    () => BillingCustomer,
    {keyTo: 'billingCustomerId'},
    {name: 'billing_customer_id'},
  )
  billingCustomerId: string;

  /**
   * The total amount to be paid for this invoice (excluding tax and discounts).
   */
  @property({
    type: 'number',
    required: true,
  })
  amount: number;

  /**
   * The tax applied to this invoice.
   */
  @property({
    type: 'number',
    required: true,
  })
  tax: number;

  /**
   * The discount applied to this invoice, if any.
   */
  @property({
    type: 'number',
    required: false,
  })
  discount: number;

  /**
   * The due date for the invoice payment.
   */
  @property({
    name: 'due_date',
    type: 'string',
    required: true,
    description: 'due date for the invoice',
  })
  dueDate: string;

  constructor(data?: Partial<Invoice>) {
    super(data);
  }
}
