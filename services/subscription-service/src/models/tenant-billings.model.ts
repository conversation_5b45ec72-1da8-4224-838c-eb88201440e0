import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {BillingCustomer} from './billing-customer.model';
import {InvoiceStatus} from '@local/core';

/**
 * Represents a view of tenant billing information, mapped to the 'tenant_billings_view' database view.
 *
 * Extends {@link UserModifiableEntity} to include user modification metadata.
 *
 * @property {string} [id] - Unique identifier for the billing record (auto-generated).
 * @property {string} invoiceId - Identifier for the invoice.
 * @property {InvoiceStatus} [invoiceStatus] - Status of the invoice or payment.
 * @property {string} billingCustomerId - Foreign key referencing the billing customer.
 * @property {number} amount - Total amount for the invoice.
 * @property {number} tax - Tax amount applied to the invoice.
 * @property {number} [discount] - Discount applied to the invoice, if any.
 * @property {string} dueDate - Due date for the invoice.
 * @property {string} customerId - Identifier for the customer (tenant).
 * @property {string} tenantId - Identifier for the tenant.
 * @property {string} tenantName - Name of the tenant.
 *
 * @remarks
 * The model is intended to be used as a read-only view for tenant billing data aggregation.
 */
@model({
  name: 'tenant_billings_view',
})
export class TenantBillingsView extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    name: 'invoice_id',
    required: true,
  })
  invoiceId: string;

  @property({
    type: 'string',
    name: 'invoice_status',
    description: 'payment or invoice status',
  })
  invoiceStatus?: InvoiceStatus;

  @belongsTo(
    () => BillingCustomer,
    {keyTo: 'billingCustomerId'},
    {name: 'billing_customer_id'},
  )
  billingCustomerId: string;

  @property({
    type: 'number',
    required: true,
  })
  amount: number;

  @property({
    type: 'number',
    required: true,
  })
  tax: number;

  @property({
    type: 'number',
    required: false,
  })
  discount: number;

  @property({
    name: 'due_date',
    type: 'string',
    required: true,
    description: 'due date for the invoice',
  })
  dueDate: string;

  @property({
    type: 'string',
    name: 'tenant_id',
    required: true,
  })
  customerId: string;

  @property({
    type: 'string',
    name: 'tenant_id',
    required: true,
  })
  tenantId: string;

  @property({
    type: 'string',
    name: 'tenant_name',
    required: true,
  })
  tenantName: string;

  constructor(data?: Partial<TenantBillingsView>) {
    super(data);
  }
}
