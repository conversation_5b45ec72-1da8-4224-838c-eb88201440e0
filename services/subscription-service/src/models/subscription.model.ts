import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {Plan} from './plan.model';
import {SubscriptionStatus} from '@sourceloop/ctrl-plane-subscription-service';
import {numericEnumValues} from '@local/core';

/**
 * Subscription model represents the subscription details of a subscriber
 * including start/end dates, cost, users, external reference IDs, status,
 * and the associated plan.
 */
@model({
  name: 'subscriptions',
})
export class Subscription extends UserModifiableEntity {
  /**
   * Unique identifier of the subscription.
   */
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id: string;

  /**
   * ID of the subscriber associated with this subscription.
   */
  @property({
    type: 'string',
    required: true,
    name: 'subscriber_id',
  })
  subscriberId: string;

  /**
   * Start date of the subscription.
   */
  @property({
    type: 'string',
    name: 'start_date',
  })
  startDate?: string;

  /**
   * End date of the subscription.
   */
  @property({
    type: 'string',
    name: 'end_date',
  })
  endDate?: string;

  /**
   * Total cost of the tenant's subscription.
   */
  @property({
    type: 'number',
    name: 'total_cost',
    description: 'total cost of the tenant',
    required: true,
  })
  totalCost: number;

  /**
   * Number of users allowed for the tenant under this subscription.
   */
  @property({
    type: 'number',
    description: 'number of users for the tenant',
    name: 'number_of_users',
  })
  numberOfUsers?: number;

  /**
   * External subscription ID (from external systems such as Stripe).
   */
  @property({
    type: 'string',
    required: true,
    name: 'external_subscription_id',
  })
  externalSubscriptionId: string;

  /**
   * Price reference ID of the subscription.
   */
  @property({
    type: 'string',
    description: 'price reference ID of the subscription.',
    required: true,
    name: 'price_ref_id',
  })
  priceRefId: string;

  /**
   * Status of the subscription.
   * Possible values:
   * - 0: pending
   * - 1: active
   * - 2: inactive
   * - 3: cancelled
   * - 4: expired
   */
  @property({
    type: 'number',
    required: true,
    description:
      'status of the subscription, it can be - 0(pending), 1(active), 2(inactive), 3(cancelled) and 4(expired)',
    jsonSchema: {
      enum: numericEnumValues(SubscriptionStatus),
    },
  })
  status: SubscriptionStatus;

  /**
   * Plan ID associated with the subscription.
   */
  @belongsTo(() => Plan, undefined, {
    description: 'plan id of the subscription',
    name: 'plan_id',
  })
  planId: string;

  /**
   * Creates an instance of Subscription.
   * @param data - Partial subscription data.
   */
  constructor(data?: Partial<Subscription>) {
    super(data);
  }
}

/**
 * Interface for subscription relations.
 */
export interface SubscriptionRelations {
  plan: Plan;
}

/**
 * Subscription type including its relations.
 */
export type SubscriptionWithRelations = Subscription & SubscriptionRelations;
