import {model, property} from '@loopback/repository';
import {CustomerDto as BillingCustomerDto} from '@sourceloop/ctrl-plane-subscription-service';

/**
 * Data Transfer Object (DTO) for Customer.
 *
 * Extends the base BillingCustomerDto from the subscription service
 * and adds additional properties specific to this context.
 */
@model()
export class CustomerDto extends BillingCustomerDto {
  /**
   * Optional name of the customer.
   */
  @property({
    type: 'string',
  })
  name?: string;

  /**
   * Creates an instance of CustomerDto.
   *
   * @param data - Partial data to initialize the DTO.
   */
  constructor(data?: Partial<CustomerDto>) {
    super(data);
  }
}
