import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, repository} from '@loopback/repository';
import {
  DefaultUserModifyCrudRepository,
  IAuthUserWithPermissions,
} from '@sourceloop/core';
import {SubscriptionDbSourceName} from '@sourceloop/ctrl-plane-subscription-service';
import {AuthenticationBindings} from 'loopback4-authentication';
import {SubscriptionServiceDataSource} from '../datasources';
import {BillingCustomer, TenantBillingsView} from '../models';
import {BillingCustomerRepository} from './billing-customer.repository';

export class TenantBillingsViewRepository extends DefaultUserModifyCrudRepository<
  TenantBillingsView,
  typeof TenantBillingsView.prototype.id,
  {}
> {
  public readonly billingCustomer: BelongsToAccessor<
    BillingCustomer,
    typeof TenantBillingsView.prototype.billingCustomerId
  >;

  constructor(
    @inject(`datasources.${SubscriptionDbSourceName}`)
    dataSource: SubscriptionServiceDataSource,
    @repository.getter('BillingCustomerRepository')
    protected billingCustomerRepositoryGetter: Getter<
      BillingCustomerRepository<BillingCustomer>
    >,

    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
  ) {
    super(TenantBillingsView, dataSource, getCurrentUser);
    this.billingCustomer = this.createBelongsToAccessorFor(
      'billingCustomer',
      this.billingCustomerRepositoryGetter,
    );

    this.registerInclusionResolver(
      'billingCustomer',
      this.billingCustomer.inclusionResolver,
    );
  }
}
