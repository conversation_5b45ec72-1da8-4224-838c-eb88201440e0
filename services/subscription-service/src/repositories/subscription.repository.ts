import {inject, Getter} from '@loopback/core';
import {
  repository,
  BelongsToAccessor,
  juggler,
  Entity,
} from '@loopback/repository';
import {Subscription, SubscriptionRelations, Plan} from '../models';
import {PlanRepository} from './plan.repository';
import {AuthenticationBindings} from 'loopback4-authentication';
import {
  DefaultUserModifyCrudRepository,
  IAuthUserWithPermissions,
} from '@sourceloop/core';
import {SubscriptionDbSourceName} from '@sourceloop/ctrl-plane-subscription-service';

/**
 * Repository for managing {@link Subscription} entities.
 *
 * Extends {@link DefaultUserModifyCrudRepository} to include user modification tracking
 * and adds a relation to {@link Plan} via a BelongsToAccessor.
 *
 * @typeParam T - Extends {@link Subscription}, defaults to Subscription.
 */
export class SubscriptionRepository<
  T extends Subscription = Subscription,
> extends DefaultUserModifyCrudRepository<
  T,
  typeof Subscription.prototype.id,
  SubscriptionRelations
> {
  /**
   * BelongsTo accessor for the {@link Plan} related to a subscription.
   */
  public readonly plan: BelongsToAccessor<
    Plan,
    typeof Subscription.prototype.id
  >;

  /**
   * Creates an instance of {@link SubscriptionRepository}.
   *
   * @param dataSource - The datasource injected for Subscription entities.
   * @param getCurrentUser - A getter function that resolves the current authenticated user.
   * @param planRepositoryGetter - A getter for the {@link PlanRepository}.
   * @param subscription - The Subscription model class.
   */
  constructor(
    @inject(`datasources.${SubscriptionDbSourceName}`)
    dataSource: juggler.DataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
    @repository.getter('PlanRepository')
    protected planRepositoryGetter: Getter<PlanRepository>,
    @inject('models.Subscription')
    private readonly subscription: typeof Entity & {prototype: T},
  ) {
    super(subscription, dataSource, getCurrentUser);

    // Setup belongs-to relation with Plan
    this.plan = this.createBelongsToAccessorFor('plan', planRepositoryGetter);
    this.registerInclusionResolver('plan', this.plan.inclusionResolver);
  }
}
