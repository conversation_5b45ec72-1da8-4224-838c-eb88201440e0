import {Getter, inject} from '@loopback/core';
import {
  DefaultUserModifyCrudRepository,
  IAuthUserWithPermissions,
} from '@sourceloop/core';
import {AuthenticationBindings} from 'loopback4-authentication';
import {juggler} from '@loopback/repository';
import {SubscriptionDbSourceName} from '@sourceloop/ctrl-plane-subscription-service';
import {ConfigureDevice} from '../models';
export class ConfigureDeviceRepository extends DefaultUserModifyCrudRepository<
  ConfigureDevice,
  typeof ConfigureDevice.prototype.id,
  {}
> {
  constructor(
    @inject(`datasources.${SubscriptionDbSourceName}`)
    dataSource: juggler.DataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
  ) {
    super(ConfigureDevice, dataSource, getCurrentUser);
  }
}
