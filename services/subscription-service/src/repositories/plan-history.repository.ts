import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, repository} from '@loopback/repository';
import {
  DefaultUserModifyCrudRepository,
  IAuthUserWithPermissions,
} from '@sourceloop/core';
import {SubscriptionDbSourceName} from '@sourceloop/ctrl-plane-subscription-service';
import {AuthenticationBindings} from 'loopback4-authentication';
import {SubscriptionServiceDataSource} from '../datasources';
import {Plan, PlanHistory} from '../models';
import {PlanRepository} from './plan.repository';

export class PlanHistoryRepository extends DefaultUserModifyCrudRepository<
  PlanHistory,
  typeof PlanHistory.prototype.id,
  {}
> {
  public readonly plan: BelongsToAccessor<
    Plan,
    typeof PlanHistory.prototype.id
  >;

  constructor(
    @inject(`datasources.${SubscriptionDbSourceName}`)
    dataSource: SubscriptionServiceDataSource,
    @repository.getter('PlanRepository')
    protected planRepositoryGetter: Getter<PlanRepository>,

    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
  ) {
    super(PlanHistory, dataSource, getCurrentUser);
    this.plan = this.createBelongsToAccessorFor('plan', planRepositoryGetter);
    this.registerInclusionResolver('plan', this.plan.inclusionResolver);
  }
}
