import {expect} from '@loopback/testlab';
import {StripeService} from '../../services/stripe.service';
import {StripeConfig} from 'loopback4-billing';
import sinon from 'sinon';
import Stripe from 'stripe';
import {CollectionMethod, RecurringInterval} from '../../types';

const mockInvoice = {
  id: 'inv_123',
  total: 1000,
  currency: 'usd',
} as Stripe.Invoice;

describe('StripeService', () => {
  let stripeService: StripeService;
  const invoiceCreateStub = sinon.stub().resolves({id: 'inv_123'});
  const invoiceFinalizeStub = sinon.stub().resolves({id: 'inv_123'});
  let stripeMock: sinon.SinonStubbedInstance<Stripe>;

  beforeEach(() => {
    const mockConfig: StripeConfig = {
      secretKey: 'test_secret_key',
    };

    // Create a fake Stripe instance
    stripeMock = {
      products: {
        create: sinon.stub().resolves({id: 'prod_123'}),
      },
      plans: {
        create: sinon.stub().resolves({id: 'plan_123'}),
      },
      subscriptions: {
        create: sinon.stub().resolves({id: 'sub_123'}),
        cancel: sinon.stub().resolves(),
      },
      invoices: {
        create: invoiceCreateStub,
        retrieve: sinon.stub().resolves(mockInvoice),
        finalizeInvoice: invoiceFinalizeStub,
        list: sinon.stub().resolves({data: []}),
        voidInvoice: sinon.stub().resolves(),
        sendInvoice: sinon.stub().resolves(),
      },
      invoiceItems: {
        create: sinon.stub().resolves({id: 'item_123'}),
      },
      customers: {
        create: sinon.stub().resolves({
          id: 'cus_123',
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '1234567890',
        }),
      },
    } as unknown as sinon.SinonStubbedInstance<Stripe>;

    // Inject mock Stripe instance into service
    stripeService = new StripeService(mockConfig);
    (
      stripeService as unknown as {
        stripeInstance: sinon.SinonStubbedInstance<Stripe>;
      }
    ).stripeInstance = stripeMock;
  });

  afterEach(() => {
    sinon.restore(); // Restore all mocks
  });

  it('should create a product', async () => {
    const mockProduct = {id: 'prod_123'};
    (stripeMock.products.create as sinon.SinonStub).resolves(mockProduct);

    const productId = await stripeService.createProduct({
      name: 'Test Product',
      description: 'Test description',
      metadata: {key: 'value'},
      priceData: {
        currency: 'usd',
        unitAmount: 1000,
        recurringInterval: RecurringInterval.MONTH,
        recurringCount: 1,
      },
    });

    expect(productId).to.equal('prod_123');
    sinon.assert.calledOnce(stripeMock.products.create as sinon.SinonSpy);
  });

  it('should create a subscription', async () => {
    const mockSubscription = {id: 'sub_123'};
    (stripeMock.subscriptions.create as sinon.SinonStub).resolves(
      mockSubscription,
    );

    const subscriptionId = await stripeService.createSubscription({
      customerId: 'cus_123',
      priceRefId: 'plan_123',
      collectionMethod: CollectionMethod.SEND_INVOICE,
      daysUntilDue: 7,
    });

    expect(subscriptionId).to.equal('sub_123');
    sinon.assert.calledOnce(stripeMock.subscriptions.create as sinon.SinonSpy);
  });

  it('should retrieve an invoice', async () => {
    (stripeMock.invoices.retrieve as sinon.SinonStub).resolves(mockInvoice);

    const invoice = await stripeService.retrieveInvoice('inv_123');

    expect(invoice).to.have.property('id', 'inv_123');
    expect(invoice).to.have.property('currencyCode', 'usd');
    sinon.assert.calledOnce(stripeMock.invoices.retrieve as sinon.SinonSpy);
  });

  it('should get invoice price details', async () => {
    const mockInvoiceDto = {
      id: 'inv_123',
      total: 2000,
      currency: 'usd',
      //eslint-disable-next-line @typescript-eslint/naming-convention
      total_tax_amounts: [{amount: 200}],
    } as Stripe.Invoice;
    (stripeMock.invoices.retrieve as sinon.SinonStub).resolves(mockInvoiceDto);

    const priceDetails = await stripeService.getInvoicePriceDetails('inv_123');

    expect(priceDetails.currency).to.equal('USD');
    expect(priceDetails.totalAmount).to.equal(2000);
    expect(priceDetails.taxAmount).to.equal(200);
    expect(priceDetails.amountExcludingTax).to.equal(1800);
    sinon.assert.calledOnce(stripeMock.invoices.retrieve as sinon.SinonSpy);
  });

  it('should create an invoice, add items, and finalize it', async () => {
    const invoiceData = {
      customerId: 'cus_123',
      options: {autoAdvnace: true},
      shippingAddress: {
        city: 'City',
        country: 'Country',
        line1: 'Line 1',
        line2: 'Line 2',
        line3: 'Line 3',
        zip: '12345',
        state: 'State',
      },
      dueDate: new Date().toISOString(),
      charges: [{amount: 1000, currency: 'usd', description: 'Test charge'}],
      currencyCode: 'usd',
    };

    const result = await stripeService.createInvoice(invoiceData);

    expect(result).to.have.property('id');
    sinon.assert.calledOnce(stripeMock.invoices.create as sinon.SinonSpy);
    sinon.assert.calledOnce(stripeMock.invoiceItems.create as sinon.SinonSpy);
    sinon.assert.calledOnce(
      stripeMock.invoices.finalizeInvoice as sinon.SinonSpy,
    );
  });

  it('should cancel subscription and void invoices correctly', async () => {
    const mockInvoices = {
      data: [
        {id: 'inv1', status: 'open'},
        {id: 'inv2', status: 'draft'},
        {id: 'inv3', status: 'paid'},
      ],
    };

    stripeMock.invoices.list = sinon.stub().resolves(mockInvoices);
    const voidStub = sinon.stub();
    const finalizeStub = sinon.stub();

    (stripeService as unknown as {voidInvoice: sinon.SinonStub}).voidInvoice =
      voidStub;
    (
      stripeService as unknown as {finalizeInvoice: sinon.SinonStub}
    ).finalizeInvoice = finalizeStub;

    await stripeService.cancelSubscription('sub_123');

    sinon.assert.calledWith(
      stripeMock.subscriptions.cancel as sinon.SinonSpy,
      'sub_123',
    );
    sinon.assert.calledWith(voidStub, 'inv2');
    sinon.assert.calledWith(finalizeStub, 'inv2');
  });

  it('should send a payment link', async () => {
    stripeMock.invoices.sendInvoice = sinon.stub().resolves();
    await stripeService.sendPaymentLink('inv_123');
    sinon.assert.calledWith(
      stripeMock.invoices.sendInvoice as sinon.SinonSpy,
      'inv_123',
    );
  });

  it('should create a customer', async () => {
    (stripeService as unknown as {voidInvoice: sinon.SinonStub}).voidInvoice =
      sinon.stub().resolves();

    await stripeService.createCustomer({
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '1234567890',
    });

    sinon.assert.calledOnce(stripeMock.customers.create as sinon.SinonSpy);
  });

  it('should create a plan', async () => {
    stripeMock.plans.create = sinon.stub().resolves({id: 'plan_123'});

    const result = await stripeService.createPlan({
      productId: 'prod_123',
      amount: 1000,
      currency: 'usd',
      recurringInterval: RecurringInterval.MONTH,
      recurringCount: 1,
      metadata: {tier: 'basic'},
    });

    expect(result).to.equal('plan_123');
  });
});
