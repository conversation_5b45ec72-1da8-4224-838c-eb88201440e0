/* eslint-disable @typescript-eslint/naming-convention */
import {StripeEventPayload, StripeEvents} from '@local/core';

export const getStripeUpdateEventPayload = (): StripeEventPayload => {
  return {
    type: StripeEvents.INVOICE_UPDATED,
    data: {
      object: {
        id: 'inv_123',
        customer: 'cus_123',
        amount_due: 5000,
        currency: 'usd',
        status: 'paid',
        total_tax_amounts: [{amount: 500}],
        total_discount_amounts: [{amount: 200}],
        period_start: 1700000000,
        period_end: 1705000000,
        due_date: 1706000000,
        number: 'INV-001',
        hosted_invoice_url: 'https://invoice.url',
        lines: {
          data: [
            {
              period: {
                start: 1700000000,
                end: 1705000000,
              },
            },
          ],
        },
      },
    },
  } as unknown as StripeEventPayload;
};
