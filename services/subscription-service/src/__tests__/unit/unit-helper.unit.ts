import {expect} from '@loopback/testlab';
import {UnitHelperService} from '../../services/unit-helper.service';

describe('UnitHelperService', () => {
  let unitHelperService: UnitHelperService;

  beforeEach(() => {
    unitHelperService = new UnitHelperService();
  });

  it('should convert USD to minor unit (cents)', () => {
    const amount = 10; // 10 dollars
    const currency = 'USD';
    const result = unitHelperService.convertToMinorUnit(amount, currency);
    expect(result).to.equal(1000); // 10 * 100 cents
  });

  it('should convert USD to major unit (dollars)', () => {
    const amount = 1000; // 1000 cents
    const currency = 'USD';
    const result = unitHelperService.convertToMajorUnit(amount, currency);
    expect(result).to.equal(10); // 1000 / 100 dollars
  });

  it('should handle unknown currency with default conversion rate', () => {
    const amount = 5;
    const currency = 'UNKNOWN';
    const result = unitHelperService.convertToMinorUnit(amount, currency);
    expect(result).to.equal(500); // 5 * 100 (default rate)
  });

  it('should convert unknown currency to major unit with default rate', () => {
    const amount = 500;
    const currency = 'UNKNOWN';
    const result = unitHelperService.convertToMajorUnit(amount, currency);
    expect(result).to.equal(5); // 500 / 100 (default rate)
  });
});
