import {PermissionKey} from '@local/core';
import {Client, expect} from '@loopback/testlab';
import {STATUS_CODE} from '@sourceloop/core';
import {SubscriptionServiceApplication} from '../../application';
import {PlanHistory} from '../../models';
import {PlanHistoryRepository} from '../../repositories';
import {getRepo, getToken, setupApplication} from './test-helper';

describe('PlanHistoryController', () => {
  let app: SubscriptionServiceApplication;
  let client: Client;
  let planHistoryRepository: PlanHistoryRepository;

  const mockPlanHistory: PlanHistory = {
    price: 99.99,
    version: 'v1',
    planId: 'plan-123',
    createdOn: new Date().toISOString(),
    modifiedOn: new Date().toISOString(),
    deleted: false,
  } as unknown as PlanHistory;

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
    planHistoryRepository = await getRepo(
      app,
      'repositories.PlanHistoryRepository',
    );
  });

  after(async () => {
    await app.stop();
  });

  beforeEach(async () => {
    await planHistoryRepository.create(mockPlanHistory);
  });

  afterEach(async () => {
    await planHistoryRepository.deleteAllHard();
  });

  it('invokes GET /plan-histories with valid token', async () => {
    const token = getToken([PermissionKey.ViewPlanHistory]);
    const {body} = await client
      .get('/plan-histories')
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);

    expect(body).to.be.Array();
    expect(body[0]).to.containEql({
      price: mockPlanHistory.price,
      version: mockPlanHistory.version,
      planId: mockPlanHistory.planId,
    });
  });

  it('invokes POST /plan-histories with valid token', async () => {
    const token = getToken([PermissionKey.CreatePlanHistory]);
    const {body} = await client
      .post('/plan-histories')
      .set('Authorization', token)
      .send({
        price: 99.99,
        version: 'v1',
        planId: 'plan-123',
      })
      .expect(STATUS_CODE.OK);

    expect(body).to.containEql({
      price: mockPlanHistory.price,
      version: mockPlanHistory.version,
      planId: mockPlanHistory.planId,
    });
  });

  it('invokes PATCH /plan-histories with valid token', async () => {
    const token = getToken([PermissionKey.UpdatePlanHistory]);
    const {body} = await client
      .patch('/plan-histories')
      .set('Authorization', token)
      .send({price: 120.5})
      .expect(STATUS_CODE.OK);

    expect(body).to.have.property('count', 1);
  });
});
