import {Client, expect} from '@loopback/testlab';
import {mockBillingCustomer} from './mock-data';
import {getRepo, getToken, setupApplication} from './test-helper';
import {STATUS_CODE} from '@sourceloop/core';
import {SubscriptionServiceApplication} from '../../application';
import {PermissionKey} from '@local/core';
import {IBillingService} from '../../types';
import {StripeService} from '../../services';
import sinon from 'sinon';
import {BillingComponentBindings} from 'loopback4-billing';
import {BillingCustomerRepository} from '../../repositories';

const basePath = '/billing-customer';

describe('BillingCustomerController', () => {
  let app: SubscriptionServiceApplication;
  let client: Client;

  let billingCustomerRepo: BillingCustomerRepository;

  let billingProvider: sinon.SinonStubbedInstance<IBillingService>;

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
    billingCustomerRepo = await getRepo(
      app,
      'repositories.BillingCustomerRepository',
    );

    billingProvider = sinon.createStubInstance<IBillingService>(StripeService);
    billingProvider.createCustomer.resolves({
      id: 'cus_123',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
    });
    billingProvider.getCustomers.resolves({
      id: 'cus_123',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
    });

    billingProvider.updateCustomerById.resolves();
    billingProvider.deleteCustomer.resolves();
    app.bind(BillingComponentBindings.SDKProvider).to(billingProvider);
  });

  after(async () => {
    await app.stop();
  });

  beforeEach(async () => {
    await seedData();
  });

  afterEach(async () => {
    await billingCustomerRepo.deleteAllHard();
  });
  it('should create billing customer on POST /billing-customer', async () => {
    const token = getToken([PermissionKey.CreateBillingCustomer]);

    const payload = {
      firstName: 'New',
      lastName: 'Customer',
      email: '<EMAIL>',
      company: 'Acme',
      phone: '**********',
      billingAddress: {
        firstName: 'New',
        lastName: 'Customer',
        email: '<EMAIL>',
        company: 'Acme',
        phone: '**********',
        city: 'City',
        state: 'State',
        zip: '12345',
        country: 'IN',
      },
    };

    const res = await client
      .post(basePath)
      .set('Authorization', token)
      .set('tenantId', 'tenant_new')
      .send(payload)
      .expect(STATUS_CODE.OK);

    // verify response shape and values returned from your controller
    expect(res.body).to.have.property('id', 'cus_123');
    expect(res.body).to.have.property('email', '<EMAIL>');
    expect(res.body).to.have.property('firstName', 'Test');

    // verify DB entry created in BillingCustomerRepository
    const created = await billingCustomerRepo.findOne({
      where: {customerId: res.body.id},
    });
    expect(created).to.not.be.null();
    expect(created!.tenantId).to.equal('tenant_new');
  });

  it('should return billing customer on GET /billing-customer with token', async () => {
    const token = getToken([PermissionKey.GetBillingCustomer]);

    const res = await client
      .get(basePath)
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);

    // controller returns { customerDetails, info }
    expect(res.body).to.have.properties(['customerDetails', 'info']);
    const customerDetails = res.body.customerDetails;
    const info = res.body.info;

    expect(customerDetails).to.have.property('email', '<EMAIL>');
    expect(customerDetails).to.have.property('firstName', 'Test');
    expect(info).to.have.property('tenantId', mockBillingCustomer.tenantId);
  });

  it('should update billing customer on PATCH /billing-customer/{tenantId}', async () => {
    const token = getToken([PermissionKey.UpdateBillingCustomer]);

    const partialUpdate = {
      firstName: 'UpdatedFirst',
      phone: '**********',
    };

    await client
      .patch(`${basePath}/${mockBillingCustomer.tenantId}`)
      .set('Authorization', token)
      .send(partialUpdate)
      .expect(STATUS_CODE.NO_CONTENT);

    // billingProvider.updateCustomerById should have been called
    sinon.assert.calledOnce(billingProvider.updateCustomerById);
    const createdCustomers = await billingCustomerRepo.find({
      where: {tenantId: mockBillingCustomer.tenantId},
    });
    expect(createdCustomers).to.not.be.empty();
    const cust = createdCustomers[0];
    sinon.assert.calledWithMatch(
      billingProvider.updateCustomerById as sinon.SinonStub,
      cust.customerId,
      partialUpdate,
    );
  });

  it('should delete billing customer on DELETE /billing-customer/{tenantId}', async () => {
    const token = getToken([PermissionKey.DeleteBillingCustomer]);

    // create an invoice tied to the seeded billing customer id
    const customers = await billingCustomerRepo.find({
      where: {tenantId: mockBillingCustomer.tenantId},
    });
    expect(customers.length).to.be.greaterThan(0);
    const billingCustomer = customers[0];

    // Call delete endpoint
    await client
      .del(`${basePath}/${mockBillingCustomer.tenantId}`)
      .set('Authorization', token)
      .expect(STATUS_CODE.NO_CONTENT);

    // billingProvider.deleteCustomer should be called with the external customer id
    sinon.assert.calledOnce(billingProvider.deleteCustomer);
    sinon.assert.calledWith(
      billingProvider.deleteCustomer as sinon.SinonStub,
      billingCustomer.customerId,
    );

    // billingCustomer record should be removed
    const customerAfter = await billingCustomerRepo.findOne({
      where: {id: billingCustomer.id},
    });
    expect(customerAfter).to.be.null();
  });

  it('should not allow GET /billing-customer without token', async () => {
    await client.get(basePath).expect(STATUS_CODE.UNAUTHORISED);
  });

  async function seedData() {
    await billingCustomerRepo.create({
      ...mockBillingCustomer,
    });
  }
});
