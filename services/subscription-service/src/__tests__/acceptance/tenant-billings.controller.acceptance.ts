// Copyright (c) SourceLoop. All rights reserved.

import {Client, expect} from '@loopback/testlab';
import {SubscriptionServiceApplication} from '../..';
import {TenantBillingsViewRepository} from '../../repositories';
import {getRepo, getToken, setupApplication} from './test-helper';
import {STATUS_CODE} from '@sourceloop/core';
import {PermissionKey, InvoiceStatus} from '@local/core';

describe('TenantBillingsController (acceptance)', () => {
  let app: SubscriptionServiceApplication;
  let client: Client;
  let tenantBillingsViewRepo: TenantBillingsViewRepository;

  const basePath = '/tenant-billings';

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
    tenantBillingsViewRepo = await getRepo(
      app,
      'repositories.TenantBillingsViewRepository',
    );
  });

  after(async () => {
    await app.stop();
  });

  beforeEach(async () => {
    await seedData();
  });

  afterEach(async () => {
    await tenantBillingsViewRepo.deleteAllHard();
  });

  it('GET /tenant-billings returns all tenant billings', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    const {body} = await client
      .get(basePath)
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);

    expect(body).to.be.Array();
    expect(body[0]).to.containEql({
      tenantId: 'tenant-1',
      invoiceStatus: InvoiceStatus.PAID,
    });
  });

  it('GET /tenant-billings/count returns count', async () => {
    const token = getToken([PermissionKey.ViewTenantBillings]);
    const {body} = await client
      .get(`${basePath}/count`)
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);

    expect(body).to.have.property('count', 1);
  });

  it('GET /tenant-billings/all-status returns all invoice statuses', async () => {
    const token = getToken([PermissionKey.ViewAllStatuses]);
    const {body} = await client
      .get(`${basePath}/all-status`)
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);

    expect(body).to.deepEqual({statuses: InvoiceStatus});
  });

  it('forbids GET /tenant-billings without proper permission', async () => {
    const token = getToken([PermissionKey.ViewPlan]);
    await client
      .get(basePath)
      .set('Authorization', token)
      .expect(STATUS_CODE.FORBIDDEN);
  });

  async function seedData() {
    await tenantBillingsViewRepo.create({
      tenantId: 'tenant-1',
      tenantName: 'Test Tenant',
      customerId: 'cust-1',
      invoiceStatus: InvoiceStatus.PAID,
      invoiceId: 'inv-1',
      amount: 1000,
      tax: 100,
      dueDate: new Date().toISOString(),
      createdOn: new Date().toISOString(),
    });
  }
});
