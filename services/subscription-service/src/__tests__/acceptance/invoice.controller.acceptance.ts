// Copyright (c) SourceLoop. All rights reserved.

import {Client, expect} from '@loopback/testlab';
import {SubscriptionServiceApplication} from '../..';
import {InvoiceRepository, BillingCustomerRepository} from '../../repositories';
import {getRepo, getToken, setupApplication} from './test-helper';
import {STATUS_CODE} from '@sourceloop/core';
import {PermissionKey, InvoiceStatus} from '@local/core';

describe('BillingInvoiceControllerV2 (acceptance)', () => {
  let app: SubscriptionServiceApplication;
  let client: Client;
  let invoiceRepo: InvoiceRepository;
  let billingCustomerRepo: BillingCustomerRepository;

  const basePath = '/billing-invoice';

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
    invoiceRepo = await getRepo(app, 'repositories.InvoiceRepository');
    billingCustomerRepo = await getRepo(
      app,
      'repositories.BillingCustomerRepository',
    );
  });

  after(async () => {
    await app.stop();
  });

  beforeEach(async () => {
    await seedData();
  });

  afterEach(async () => {
    await invoiceRepo.deleteAllHard();
    await billingCustomerRepo.deleteAllHard();
  });

  describe('GET /billing-invoice', () => {
    it('returns all invoices when authorized', async () => {
      const token = getToken([PermissionKey.ViewTenantBillings]);
      const {body} = await client
        .get(basePath)
        .set('Authorization', token)
        .expect(STATUS_CODE.OK);

      expect(body).to.be.Array();
      expect(body).to.have.length(3);
      expect(body[0]).to.containEql({
        invoiceId: 'INV-001',
        invoiceStatus: InvoiceStatus.PAID,
        amount: 1000,
        tax: 100,
        discount: 50,
      });
    });

    it('returns filtered invoices when filter is provided', async () => {
      const token = getToken([PermissionKey.ViewTenantBillings]);
      const filter = {
        where: {
          invoiceStatus: InvoiceStatus.PAID,
        },
      };

      const {body} = await client
        .get(basePath)
        .set('Authorization', token)
        .query({filter: JSON.stringify(filter)})
        .expect(STATUS_CODE.OK);

      expect(body).to.be.Array();
      expect(body).to.have.length(1);
      expect(body[0]).to.containEql({
        invoiceStatus: InvoiceStatus.PAID,
      });
    });

    it('returns invoices with amount filter', async () => {
      const token = getToken([PermissionKey.ViewTenantBillings]);
      const filter = {
        where: {
          amount: {gte: 1500},
        },
      };

      const {body} = await client
        .get(basePath)
        .set('Authorization', token)
        .query({filter: JSON.stringify(filter)})
        .expect(STATUS_CODE.OK);

      expect(body).to.be.Array();
      expect(body).to.have.length(2); // Both INV-002 (2000) and INV-003 (1500) match
      expect(
        body.some((invoice: any) => invoice.invoiceId === 'INV-002'),
      ).to.be.true();
      expect(
        body.some((invoice: any) => invoice.invoiceId === 'INV-003'),
      ).to.be.true();
    });

    it('returns limited invoices when limit is specified', async () => {
      const token = getToken([PermissionKey.ViewTenantBillings]);
      const filter = {
        limit: 2,
        order: ['createdOn ASC'],
      };

      const {body} = await client
        .get(basePath)
        .set('Authorization', token)
        .query({filter: JSON.stringify(filter)})
        .expect(STATUS_CODE.OK);

      expect(body).to.be.Array();
      expect(body).to.have.length(2);
    });

    it('returns invoices with complex filter', async () => {
      const token = getToken([PermissionKey.ViewTenantBillings]);
      const filter = {
        where: {
          and: [
            {amount: {gte: 1000}},
            {invoiceStatus: {inq: [InvoiceStatus.OPEN, InvoiceStatus.PAID]}},
          ],
        },
        order: ['amount DESC'],
        limit: 5,
      };

      const {body} = await client
        .get(basePath)
        .set('Authorization', token)
        .query({filter: JSON.stringify(filter)})
        .expect(STATUS_CODE.OK);

      expect(body).to.be.Array();
      expect(body.length).to.be.greaterThan(0);
      // Verify ordering - first item should have highest amount
      if (body.length > 1) {
        expect(body[0].amount).to.be.greaterThanOrEqual(body[1].amount);
      }
    });

    it('returns empty array when no invoices match filter', async () => {
      const token = getToken([PermissionKey.ViewTenantBillings]);
      const filter = {
        where: {
          amount: {gt: 10000}, // No invoice has amount > 10000
        },
      };

      const {body} = await client
        .get(basePath)
        .set('Authorization', token)
        .query({filter: JSON.stringify(filter)})
        .expect(STATUS_CODE.OK);

      expect(body).to.be.Array();
      expect(body).to.have.length(0);
    });

    it('forbids access without proper permission', async () => {
      const token = getToken([PermissionKey.ViewPlan]); // Wrong permission
      await client
        .get(basePath)
        .set('Authorization', token)
        .expect(STATUS_CODE.FORBIDDEN);
    });

    it('returns 401 when no token is provided', async () => {
      await client.get(basePath).expect(STATUS_CODE.UNAUTHORISED);
    });

    it('returns 401 when invalid token is provided', async () => {
      await client
        .get(basePath)
        .set('Authorization', 'Bearer invalid-token')
        .expect(STATUS_CODE.UNAUTHORISED);
    });

    it('returns 401 when empty authorization header is provided', async () => {
      await client
        .get(basePath)
        .set('Authorization', '')
        .expect(STATUS_CODE.UNAUTHORISED);
    });
  });

  describe('GET /billing-invoice/count', () => {
    it('returns count of all invoices when authorized', async () => {
      const token = getToken([PermissionKey.ViewTenantBillings]);
      const {body} = await client
        .get(`${basePath}/count`)
        .set('Authorization', token)
        .expect(STATUS_CODE.OK);

      expect(body).to.have.property('count', 3);
    });

    it('returns filtered count when where clause is provided', async () => {
      const token = getToken([PermissionKey.ViewTenantBillings]);
      const where = {
        invoiceStatus: InvoiceStatus.PAID,
      };

      const {body} = await client
        .get(`${basePath}/count`)
        .set('Authorization', token)
        .query({where: JSON.stringify(where)})
        .expect(STATUS_CODE.OK);

      expect(body).to.have.property('count', 1);
    });

    it('returns count with amount filter', async () => {
      const token = getToken([PermissionKey.ViewTenantBillings]);
      const where = {
        amount: {gte: 1500},
      };

      const {body} = await client
        .get(`${basePath}/count`)
        .set('Authorization', token)
        .query({where: JSON.stringify(where)})
        .expect(STATUS_CODE.OK);

      expect(body).to.have.property('count', 2); // Both INV-002 (2000) and INV-003 (1500) match
    });

    it('returns count with complex where clause', async () => {
      const token = getToken([PermissionKey.ViewTenantBillings]);
      const where = {
        and: [
          {amount: {gte: 1000}},
          {invoiceStatus: {inq: [InvoiceStatus.OPEN, InvoiceStatus.PAID]}},
        ],
      };

      const {body} = await client
        .get(`${basePath}/count`)
        .set('Authorization', token)
        .query({where: JSON.stringify(where)})
        .expect(STATUS_CODE.OK);

      expect(body).to.have.property('count');
      expect(body.count).to.be.greaterThan(0);
    });

    it('returns zero count when no invoices match where clause', async () => {
      const token = getToken([PermissionKey.ViewTenantBillings]);
      const where = {
        amount: {gt: 10000}, // No invoice has amount > 10000
      };

      const {body} = await client
        .get(`${basePath}/count`)
        .set('Authorization', token)
        .query({where: JSON.stringify(where)})
        .expect(STATUS_CODE.OK);

      expect(body).to.have.property('count', 0);
    });

    it('forbids access without proper permission', async () => {
      const token = getToken([PermissionKey.ViewPlan]); // Wrong permission
      await client
        .get(`${basePath}/count`)
        .set('Authorization', token)
        .expect(STATUS_CODE.FORBIDDEN);
    });

    it('returns 401 when no token is provided', async () => {
      await client.get(`${basePath}/count`).expect(STATUS_CODE.UNAUTHORISED);
    });

    it('returns 401 when invalid token is provided', async () => {
      await client
        .get(`${basePath}/count`)
        .set('Authorization', 'Bearer invalid-token')
        .expect(STATUS_CODE.UNAUTHORISED);
    });

    it('returns 401 when empty authorization header is provided', async () => {
      await client
        .get(`${basePath}/count`)
        .set('Authorization', '')
        .expect(STATUS_CODE.UNAUTHORISED);
    });
  });

  async function seedData() {
    // Create billing customers first
    const customer1 = await billingCustomerRepo.create({
      customerId: 'cust-001',
      tenantId: 'tenant-001',
      name: 'Customer 1',
      createdOn: new Date(),
      modifiedOn: new Date(),
    });

    const customer2 = await billingCustomerRepo.create({
      customerId: 'cust-002',
      tenantId: 'tenant-002',
      name: 'Customer 2',
      createdOn: new Date(),
      modifiedOn: new Date(),
    });

    const customer3 = await billingCustomerRepo.create({
      customerId: 'cust-003',
      tenantId: 'tenant-003',
      name: 'Customer 3',
      createdOn: new Date(),
      modifiedOn: new Date(),
    });

    // Create invoices
    await invoiceRepo.create({
      invoiceId: 'INV-001',
      billingCustomerId: customer1.customerId,
      invoiceStatus: InvoiceStatus.PAID,
      amount: 1000,
      tax: 100,
      discount: 50,
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      createdOn: new Date(),
      modifiedOn: new Date(),
    });

    await invoiceRepo.create({
      invoiceId: 'INV-002',
      billingCustomerId: customer2.customerId,
      invoiceStatus: InvoiceStatus.OPEN,
      amount: 2000,
      tax: 200,
      discount: 0,
      dueDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
      createdOn: new Date(),
      modifiedOn: new Date(),
    });

    await invoiceRepo.create({
      invoiceId: 'INV-003',
      billingCustomerId: customer3.customerId,
      invoiceStatus: InvoiceStatus.DRAFT,
      amount: 1500,
      tax: 150,
      discount: 25,
      dueDate: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(),
      createdOn: new Date(),
      modifiedOn: new Date(),
    });
  }
});
