import {Client, expect} from '@loopback/testlab';
import {StripeService, StripeWebhookService} from '../../services';
import sinon from 'sinon';
import {RestApplication} from '@loopback/rest';
import {setupApplication} from './test-helper';
import Stripe from 'stripe';
import {StripeEvents} from '@local/core';
import {IBillingService} from '../../types';
import {BillingComponentBindings} from 'loopback4-billing';

describe('WebhookController', () => {
  let app: RestApplication;
  let client: Client;
  let stripeWebhookService: StripeWebhookService;
  let handleWebhookStub: sinon.SinonStub;
  let stripe: Stripe;
  let billingProvider: sinon.SinonStubbedInstance<IBillingService>;

  before(async () => {
    process.env.STRIPE_SECRET = 'test';
    process.env.STRIPE_WEBHOOK_SECRET = 'whsec_test';

    ({app, client} = await setupApplication());

    stripeWebhookService = app.getSync<StripeWebhookService>(
      'services.StripeWebhookService',
    );
    handleWebhookStub = sinon
      .stub(stripeWebhookService, 'handleWebhookEvent')
      .resolves();
    stripe = new Stripe(process.env.STRIPE_SECRET as string, {
      apiVersion: '2025-07-30.basil',
    });

    billingProvider = sinon.createStubInstance<IBillingService>(StripeService);
    billingProvider.getSubscription.resolves({
      id: 'sub_123',
      status: 'active',
    } as unknown as Stripe.Subscription);
    app.bind(BillingComponentBindings.SDKProvider).to(billingProvider);
  });

  after(async () => {
    handleWebhookStub.restore();

    await app.stop();
  });
  it('should construct event with valid test header', async () => {
    const payload = {
      type: StripeEvents.INVOICE_CREATED,
      data: {object: {id: 'inv_123'}},
      id: 'evt_test_webhook',
    };

    // Simulate Stripe signature
    const payloadString = JSON.stringify(payload, null, 2);
    const header = stripe.webhooks.generateTestHeaderString({
      payload: payloadString,
      secret: process.env.STRIPE_WEBHOOK_SECRET as string,
    });

    const event = stripe.webhooks.constructEvent(
      payloadString,
      header,
      process.env.STRIPE_WEBHOOK_SECRET as string,
    );
    expect(event.id).to.equal(payload.id);
  });

  it('should return 200 if authentication header is provided', async () => {
    process.env.ENABLE_STRIPE_WEBHOOK_VALIDATION = 'false';
    const payload = {
      type: StripeEvents.INVOICE_CREATED,
      data: {object: {id: 'inv_123'}},
      id: 'evt_test_webhook',
    };

    // Simulate Stripe signature
    const payloadString = JSON.stringify(payload, null, 2);
    const header = stripe.webhooks.generateTestHeaderString({
      payload: payloadString,
      secret: process.env.STRIPE_WEBHOOK_SECRET as string,
    });

    const event = stripe.webhooks.constructEvent(
      payloadString,
      header,
      process.env.STRIPE_WEBHOOK_SECRET as string,
    );
    const response = await client
      .post('/webhooks/payment')
      .send(event)
      .set('stripe-signature', header)
      .expect(200);
    expect(response.body.event).to.equal(StripeEvents.INVOICE_CREATED);
  });
});
