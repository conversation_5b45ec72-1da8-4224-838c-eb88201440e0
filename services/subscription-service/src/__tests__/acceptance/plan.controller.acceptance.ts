import {getEnumMap, PermissionKey, PlanStatus, PlanTierType} from '@local/core';
import {Client, expect} from '@loopback/testlab';
import {STATUS_CODE} from '@sourceloop/core';
import {SubscriptionServiceApplication} from '../../application';
import {Plan} from '../../models/plan.model';
import {PlanRepository} from '../../repositories';
import {getRepo, getToken, setupApplication} from './test-helper';
import {IBillingService} from '../../types';
import {StripeService} from '../../services';
import sinon from 'sinon';
import {BillingComponentBindings} from 'loopback4-billing';

describe('PlanController', () => {
  let app: SubscriptionServiceApplication;
  let client: Client;
  let planRepository: PlanRepository;
  let billingProvider: sinon.SinonStubbedInstance<IBillingService>;

  const mockPlan: Plan = {
    name: 'Pro Plan',
    description: 'Test Plan',
    tier: PlanTierType.PREMIUM,
    price: 100,
    metaData: {feature: 'test'},
    status: PlanStatus.ACTIVE,
    version: 'v1',
    allowedUnlimitedUsers: true,
    costPerUser: 10,
    billingCycleId: 'bc-123',
    currencyId: 'cur-123',
    configureDeviceId: 'cd-123',
    planSizeId: 'ps-123',
    createdOn: new Date().toISOString(),
    modifiedOn: new Date().toISOString(),
    deleted: false,
    productRefId: 'prod-123',
  } as unknown as Plan;

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
    planRepository = await getRepo(app, 'repositories.PlanRepository');

    billingProvider = sinon.createStubInstance<IBillingService>(StripeService);
    billingProvider.createProduct.resolves('mock-product-id');
    app.bind(BillingComponentBindings.SDKProvider).to(billingProvider);
  });

  after(async () => {
    await app.stop();
  });

  beforeEach(async () => {
    await seedData();
  });
  async function seedData() {
    await planRepository.create({
      ...mockPlan,
    });
  }

  afterEach(async () => {
    await planRepository.deleteAllHard();
    sinon.restore();
  });

  it('POST /plans - creates a plan with valid token', async () => {
    const token = getToken([PermissionKey.CreatePlan]);
    await client
      .post('/plans')
      .set('Authorization', token)
      .send({
        name: mockPlan.name,
        tier: mockPlan.tier,
        price: mockPlan.price,
        version: mockPlan.version,
        billingCycleId: mockPlan.billingCycleId,
        currencyId: mockPlan.currencyId,
        configureDeviceId: mockPlan.configureDeviceId,
        planSizeId: mockPlan.planSizeId,
      })
      .expect(STATUS_CODE.OK);
  });

  it('GET /plans/count - returns count with valid token', async () => {
    const token = getToken([PermissionKey.ViewPlan]);
    const {body} = await client
      .get('/plans/count')
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);

    expect(body).to.have.property('count');
  });

  it('GET /plans - returns all plans with valid token', async () => {
    const token = getToken([PermissionKey.ViewPlan]);
    const {body} = await client
      .get('/plans')
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);

    expect(body).to.be.Array();
    expect(body[0]).to.containEql({
      name: mockPlan.name,
      tier: mockPlan.tier,
    });
  });

  it('PATCH /plans - updates multiple plans with valid token', async () => {
    const token = getToken([PermissionKey.UpdatePlan]);
    const {body} = await client
      .patch('/plans')
      .set('Authorization', token)
      .send({price: 200})
      .expect(STATUS_CODE.OK);

    expect(body).to.have.property('count', 1);
  });

  it('GET /plans/all-status - returns all plan statuses with valid token', async () => {
    const token = getToken([PermissionKey.ViewAllStatuses]);
    const {body} = await client
      .get('/plans/all-status')
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);

    const expectedStatuses = getEnumMap(PlanStatus);
    expect(body).to.deepEqual({statuses: expectedStatuses});
  });
  it('GET /plans/count with filter', async () => {
    const token = getToken([PermissionKey.ViewPlan]);
    const {body} = await client
      .get('/plans/count')
      .query({where: {status: PlanStatus.ACTIVE}})
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);

    expect(body).to.have.property('count', 1);
  });
});
