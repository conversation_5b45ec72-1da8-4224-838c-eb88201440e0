import {SubscriptionStatus} from '@sourceloop/ctrl-plane-subscription-service';
import {BillingCustomer, Plan, Subscription} from '../../models';
import {DataObject} from '@loopback/repository';
import {PlanStatus, PlanTierType} from '@local/core';

export const mockPlan: Plan = {
  name: 'Pro Plan',
  description: 'Test Plan',
  tier: PlanTierType.PREMIUM,
  price: 100,
  metaData: {feature: 'test'},
  status: PlanStatus.ACTIVE,
  version: 'v1',
  allowedUnlimitedUsers: true,
  costPerUser: 10,
  billingCycleId: 'bc-123',
  currencyId: 'cur-123',
  configureDeviceId: 'cd-123',
  planSizeId: 'ps-123',
  createdOn: new Date().toISOString(),
  modifiedOn: new Date().toISOString(),
  deleted: false,
  productRefId: 'prod-123',
} as unknown as Plan;

export const mockSubscription: DataObject<Subscription> = {
  subscriberId: 'test-subscriber-id',
  startDate: 'sdsd',
  endDate: 'sdsd',
  status: SubscriptionStatus.ACTIVE,
  planId: 'test-plan-id',
  externalSubscriptionId: 'ext-sub-id',
  priceRefId: 'price-123',
};

export const mockBillingCustomer: DataObject<BillingCustomer> = {
  customerId: 'cus_JZ6ZQ9Q9Q9Q9Q9',
  tenantId: '60630554-5cfb-4e85-aa97-d180a690dcd9',
};
