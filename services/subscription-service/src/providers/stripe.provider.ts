import {Provider, inject} from '@loopback/core';
import {StripeBindings, StripeConfig} from 'loopback4-billing';
import {IBillingService} from '../types';
import {StripeService} from '../services';

/**
 * Provider class to supply an instance of StripeService
 * implementing the IBillingService interface.
 */
export class StripeServiceProvider implements Provider<IBillingService> {
  /**
   * Creates a new instance of StripeServiceProvider.
   *
   * @param stripeConfig - Optional Stripe configuration injected
   * from application bindings.
   */
  constructor(
    @inject(StripeBindings.config, {optional: true})
    private readonly stripeConfig: StripeConfig,
  ) {}

  /**
   * Returns an instance of StripeService which implements
   * the IBillingService interface.
   *
   * @returns An initialized StripeService instance.
   */
  value(): IBillingService {
    return new StripeService(this.stripeConfig);
  }
}
