import {IAddressDto, IService, Options, TAddress} from 'loopback4-billing';
import {InvoiceStatus as BaseInvoiceType} from '@sourceloop/ctrl-plane-subscription-service';
import {AnyObject} from '@loopback/repository';
import Stripe from 'stripe';
import {PriceDto} from '@local/core';
export interface IStripeInvoice extends TInvoice {
  shippingAddress: IAddressDto | undefined;
  options?: {
    autoAdvnace?: boolean;
  };
}
export interface ICharge {
  amount: number;
  description: string;
  taxRates?: string[];
}

export interface TInvoice {
  id?: string;
  customerId: string;
  shippingAddress?: TAddress;
  status?: BaseInvoiceType;
  charges?: ICharge[];
  options?: Options;
  currencyCode: string;
  startDate?: string;
  endDate?: string;
  dueDate?: string;
}

export interface IContent {
  invoice: TInvoice;
}

export enum PaymentStatus {
  PAID = 'paid',
  POSTED = 'posted',
  PAYMENT_DUE = 'payment_due',
  NOT_PAID = 'not_paid',
  VOIDED = 'voided',
  PENDING = 'pending',
}

export enum PaymentMethodEnum {
  Cash = 'cash',
  Check = 'check',
  BankTranser = 'bank_transfer',
  Other = 'other',
  Custom = 'custom',
  PaymentSource = 'payment_source',
}

export enum CollectionMethod {
  CHARGE_AUTOMATICALLY = 'charge_automatically',
  SEND_INVOICE = 'send_invoice',
}

export enum RecurringInterval {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  YEAR = 'year',
}
export interface InvoicePrice {
  currency: string;
  totalAmount: number;
  taxAmount: number;
  amountExcludingTax: number;
}

export interface ProductCreateParams {
  name: string;
  description?: string;
  metadata?: Record<string, string>;
  priceData?: {
    currency: string;
    unitAmount: number;
    recurringInterval: RecurringInterval;
    recurringCount: number;
  };
}

export interface PlanCreateParams {
  productId: string;
  amount: number;
  currency: string;
  recurringInterval: RecurringInterval;
  recurringCount: number;
  metadata?: Record<string, string>;
}

export interface SubscriptionCreateParams {
  customerId: string;
  priceRefId: string;
  collectionMethod: CollectionMethod;
  daysUntilDue?: number;
}
export interface IBillingService extends IService {
  getInvoicePriceDetails(invoiceId: string): Promise<InvoicePrice>;
  createProduct(product: ProductCreateParams): Promise<string>;
  createPlan(plan: PlanCreateParams): Promise<string>;
  createSubscription(subscription: SubscriptionCreateParams): Promise<string>;
  getSubscription(subscriptionId: string): Promise<Stripe.Subscription>;
  cancelSubscription(subscriptionId: string): Promise<void>;
  sendPaymentLink(invoiceId: string): Promise<void>;
  retrievePaymentIntent(paymentIntentId: string): Promise<AnyObject>;
  createPrice(price: Partial<PriceDto>): Promise<PriceDto>;
}

export enum BillingCycleDurationUnit {
  YEARLY = 'YEARLY',
  MONTHLY = 'MONTHLY',
}
