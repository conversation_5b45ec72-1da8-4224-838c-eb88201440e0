import {
  inject,
  Interceptor,
  InvocationContext,
  Provider,
  Setter,
  ValueOrPromise,
} from '@loopback/core';
import {HttpErrors, RequestContext} from '@loopback/rest';
import {LOGGER, ILogger, IAuthUserWithPermissions} from '@sourceloop/core';
import {SYSTEM_USER} from '@sourceloop/ctrl-plane-subscription-service';
import {AuthenticationBindings} from 'loopback4-authentication';
import Stripe from 'stripe';

/**
 * Provider that returns an interceptor to verify Stripe webhook requests.
 *
 * @remarks
 * - Validates the Stripe signature header and optionally constructs the Stripe event
 *   using the webhook secret when webhook validation is enabled.
 * - On successful verification, it sets the current user to a system user so downstream
 *   handlers have an authenticated context.
 * - Throws `HttpErrors.Unauthorized` when verification fails or configuration is missing.
 */
export class StripeWebhookVerifierProvider implements Provider<Interceptor> {
  /**
   * Creates an instance of StripeWebhookVerifierProvider.
   *
   * @param logger - Logger instance for logging errors and info
   * @param systemUser - System user to be set on the request context after successful verification
   * @param setCurrentUser - Setter function to assign the current user in the authentication binding
   */
  constructor(
    @inject(LOGGER.LOGGER_INJECT)
    private readonly logger: ILogger,
    @inject(SYSTEM_USER)
    private readonly systemUser: IAuthUserWithPermissions,
    @inject.setter(AuthenticationBindings.CURRENT_USER)
    private readonly setCurrentUser: Setter<IAuthUserWithPermissions>,
  ) {}

  /**
   * Returns the interceptor function bound to this provider.
   *
   * @returns The interceptor function to be used by LoopBack's interception system.
   */
  value() {
    return this.intercept.bind(this);
  }

  /**
   * Interceptor that validates incoming Stripe webhook requests.
   *
   * @typeParam T - The return type of the intercepted invocation.
   * @param invocationCtx - The invocation context which includes the parent RequestContext.
   * @param next - The next function in the invocation chain.
   * @returns The result of the next invocation if verification succeeds.
   *
   * @throws {HttpErrors.Unauthorized} When Stripe secret is not configured or
   * verification fails.
   */
  async intercept<T>(
    invocationCtx: InvocationContext,
    next: () => ValueOrPromise<T>,
  ) {
    const {request} = invocationCtx.parent as RequestContext; // NOSONAR

    const sig = request.headers['stripe-signature'];
    const stripeSecret = process.env.STRIPE_SECRET;
    if (!stripeSecret) throw new HttpErrors.Unauthorized();

    const stripe = new Stripe(stripeSecret, {
      apiVersion: '2025-07-30.basil',
    });
    try {
      const isWebhookValidationEnabled =
        process.env.ENABLE_STRIPE_WEBHOOK_VALIDATION === 'true';

      const stripeWebhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
      if (isWebhookValidationEnabled && stripeWebhookSecret) {
        stripe.webhooks.constructEvent(
          request.body,
          sig as string,
          stripeWebhookSecret,
        );
      }
      this.setCurrentUser(this.systemUser);
    } catch (error) {
      this.logger.error(error);
      throw new HttpErrors.Unauthorized();
    }

    return next();
  }
}
