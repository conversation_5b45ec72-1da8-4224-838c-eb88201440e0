import {injectable, BindingScope} from '@loopback/core';

const DEFAULT_CONVERSION_RATE = 100;

/**
 * Service to handle conversion between major and minor currency units.
 * Example: USD → dollars to cents and vice versa.
 */
@injectable({scope: BindingScope.TRANSIENT})
export class UnitHelperService {
  /**
   * Converts an amount from major unit (e.g., dollars) to minor unit (e.g., cents).
   *
   * @param amount - The amount in major units.
   * @param currency - The currency code (e.g., 'USD').
   * @returns The converted amount in minor units.
   */
  convertToMinorUnit(amount: number, currency: string): number {
    const lowerCurrency = currency.toLowerCase();

    const conversionRates: Record<string, number> = {
      usd: 100, // Dollars to Cents
    };

    const rate = conversionRates[lowerCurrency] ?? DEFAULT_CONVERSION_RATE; // Default to 100 if unknown currency
    return amount * rate;
  }

  /**
   * Converts an amount from minor unit (e.g., cents) to major unit (e.g., dollars).
   *
   * @param amount - The amount in minor units.
   * @param currency - The currency code (e.g., 'USD').
   * @returns The converted amount in major units.
   */
  convertToMajorUnit(amount: number, currency: string): number {
    const lowerCurrency = currency.toLowerCase();

    const conversionRates: Record<string, number> = {
      usd: 100,
    };
    const rate = conversionRates[lowerCurrency] ?? DEFAULT_CONVERSION_RATE; // Default to 100 if unknown
    return amount / rate;
  }
}
