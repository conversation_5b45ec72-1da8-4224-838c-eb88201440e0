/**
 *
 * Utility function to get a map of tenant status codes to their string representations.
 * This is useful for converting numeric status codes to human-readable strings.
 * @returns A map of tenant status codes to their string representations.
 */

import {TenantStatus} from '@local/core';

export function getTenantStatusMap(): {[key: number]: string} {
  return Object.keys(TenantStatus)
    .filter(key => !isNaN(Number(key)))
    .reduce(
      (acc, key) => {
        const numKey = Number(key);
        acc[numKey] = TenantStatus[numKey];
        return acc;
      },
      {} as {[key: number]: string},
    );
}
