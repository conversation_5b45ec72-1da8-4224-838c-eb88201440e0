import {Getter, inject} from '@loopback/core';
import {
  Entity,
  juggler,
  repository,
  BelongsToAccessor,
} from '@loopback/repository';
import {
  DefaultTransactionalUserModifyRepository,
  IAuthUserWithPermissions,
} from '@sourceloop/core';
import {AuthenticationBindings} from 'loopback4-authentication';
import {File, Tenant} from '../models';
import {TenantManagementDbSourceName} from '@sourceloop/ctrl-plane-tenant-management-service';
import {TenantRepository} from './tenant.repository';

export class FileRepository<
  T extends File = File,
> extends DefaultTransactionalUserModifyRepository<
  T,
  typeof File.prototype.id,
  {}
> {
  public readonly tenant: BelongsToAccessor<Tenant, typeof File.prototype.id>;

  constructor(
    @inject(`datasources.${TenantManagementDbSourceName}`)
    dataSource: juggler.DataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
    @inject('models.File')
    private readonly file: typeof Entity & {prototype: T},
    @repository.getter('TenantRepository')
    protected tenantRepositoryGetter: Getter<TenantRepository>,
  ) {
    super(file, dataSource, getCurrentUser);
    this.tenant = this.createBelongsToAccessorFor(
      'tenant',
      tenantRepositoryGetter,
    );
    this.registerInclusionResolver('tenant', this.tenant.inclusionResolver);
  }
}
