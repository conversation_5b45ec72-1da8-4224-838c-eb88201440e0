import {Getter, inject} from '@loopback/core';
import {
  BelongsToAccessor,
  <PERSON>tity,
  HasManyRepositoryFactory,
  juggler,
  repository,
} from '@loopback/repository';
import {
  DefaultTransactionalUserModifyRepository,
  IAuthUserWithPermissions,
} from '@sourceloop/core';
import {AuthenticationBindings} from 'loopback4-authentication';
import {
  TenantRelations,
  Resource,
  TenantManagementDbSourceName,
  ContactRepository,
  ResourceRepository,
  Address,
  Contact,
  AddressRepository,
  Lead,
  LeadRepository,
} from '@sourceloop/ctrl-plane-tenant-management-service';
import {FileRepository} from './file.repository';
import {Tenant, File} from '../models';

export class TenantRepository<
  T extends Tenant = Tenant,
> extends DefaultTransactionalUserModifyRepository<
  T,
  typeof Tenant.prototype.id,
  TenantRelations
> {
  public readonly contacts: HasManyRepositoryFactory<
    Contact,
    typeof Tenant.prototype.id
  >;

  public readonly resources: HasManyRepositoryFactory<
    Resource,
    typeof Tenant.prototype.id
  >;

  public readonly lead: BelongsToAccessor<Lead, typeof Tenant.prototype.id>;

  public readonly address: BelongsToAccessor<
    Address,
    typeof Tenant.prototype.id
  >;

  public readonly files: HasManyRepositoryFactory<
    File,
    typeof Tenant.prototype.id
  >;

  constructor(
    @inject(`datasources.${TenantManagementDbSourceName}`)
    dataSource: juggler.DataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
    @repository.getter('ContactRepository')
    protected contactRepositoryGetter: Getter<ContactRepository>,
    @repository.getter('LeadRepository')
    protected leadRepositoryGetter: Getter<LeadRepository>,
    @repository.getter('ResourceRepository')
    protected resourceRepositoryGetter: Getter<ResourceRepository>,
    @repository.getter('AddressRepository')
    protected addressRepositoryGetter: Getter<AddressRepository>,
    @inject('models.Tenant')
    private readonly tenant: typeof Entity & {prototype: T},
    @repository.getter('FileRepository')
    protected fileRepositoryGetter: Getter<FileRepository>,
  ) {
    super(tenant, dataSource, getCurrentUser);
    this.files = this.createHasManyRepositoryFactoryFor(
      'files',
      fileRepositoryGetter,
    );
    this.registerInclusionResolver('files', this.files.inclusionResolver);
    this.lead = this.createBelongsToAccessorFor('lead', leadRepositoryGetter);
    this.registerInclusionResolver('lead', this.lead.inclusionResolver);
    this.contacts = this.createHasManyRepositoryFactoryFor(
      'contacts',
      contactRepositoryGetter,
    );
    this.registerInclusionResolver('contacts', this.contacts.inclusionResolver);

    this.resources = this.createHasManyRepositoryFactoryFor(
      'resources',
      resourceRepositoryGetter,
    );
    this.registerInclusionResolver(
      'resources',
      this.resources.inclusionResolver,
    );

    this.address = this.createBelongsToAccessorFor(
      'address',
      addressRepositoryGetter,
    );
    this.registerInclusionResolver('address', this.address.inclusionResolver);
  }
}
