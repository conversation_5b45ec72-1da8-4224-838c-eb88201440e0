import {Permission<PERSON>ey, StatusDto} from '@local/core';
import {service} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {get, getModelSchemaRef, param, post, requestBody} from '@loopback/rest';
import {
  CONTENT_TYPE,
  OPERATION_SECURITY_SPEC,
  STATUS_CODE,
} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {KeySuggestionDto, TenantOnboardDTO, VerifyKeyDto} from '../models';
import {Tenant} from '../models/tenant.model';
import {OnboardingService} from '../services';
import {IOnboardingService} from '../types';
import {TenantRepository} from '../repositories/tenant.repository';
import {getTenantStatusMap} from '../util';
import {
  IProvisioningService,
  ProvisioningService,
  SubscriptionDTO,
  Tenant as ProvisioningTenant,
} from '@sourceloop/ctrl-plane-tenant-management-service';
const basePath = '/tenants';
const DEFAULT_NUM_KEY_SUGGESTIONS = 4;
const CANDIDATE_KEY_MULTIPLIER = 5;

/**
 * Controller for handling tenant onboarding and key verification operations.
 */
export class TenantController {
  /**
   * Creates an instance of TenantController.
   *
   * @param tenantRepository - The repository for accessing tenant data.
   * @param onboarding - The onboarding service used to handle new tenant creation.
   */
  constructor(
    @repository(TenantRepository)
    public tenantRepository: TenantRepository<Tenant>,
    @service(OnboardingService)
    private readonly onboarding: IOnboardingService<Tenant>,
    @service(ProvisioningService)
    private readonly provisioningService: IProvisioningService<SubscriptionDTO>,
  ) {}

  /**
   * Onboards a new tenant into the system.
   *
   * @remarks
   * This endpoint creates a new tenant using the provided onboarding data.
   * It returns the newly created tenant model instance.
   *
   * Requires `CreateTenant` permission and bearer token authentication.
   *
   * @param dto - The tenant onboarding data transfer object.
   * @returns The created `Tenant` instance.
   *
   * @response 200 - Returns the created tenant model instance.
   *
   * @security BearerToken
   */
  @authorize({
    permissions: [PermissionKey.CreateTenant],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @post(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Tenant model instance',
        content: {
          [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Tenant)},
        },
      },
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(TenantOnboardDTO, {
            title: 'NewTenantOnboarding',
            exclude: [],
          }),
        },
      },
    })
    dto: TenantOnboardDTO,
  ): Promise<Tenant> {
    return this.onboarding.onboard(dto);
  }

  /**
   * Verifies the availability of a tenant key.
   *
   * @remarks
   * This endpoint checks whether a given tenant key is already taken.
   * If it is taken, it generates a list of alternative suggestions based on the key.
   *
   * Requires `ViewTenant` permission and bearer token authentication.
   *
   * @param dto - The DTO containing the key to verify.
   * @returns An object indicating availability and suggested alternatives if not available.
   *
   * @response 200 - Key availability result with optional suggestions.
   *
   * @security BearerToken
   */
  @authorize({
    permissions: [PermissionKey.ViewTenant],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @post(`${basePath}/verify-key`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description:
          'couples of possible leads if not requested key is not available',
        content: {
          [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(KeySuggestionDto)},
        },
      },
    },
  })
  async verifyKey(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(VerifyKeyDto),
        },
      },
    })
    dto: VerifyKeyDto,
  ): Promise<KeySuggestionDto> {
    const tenant = await this.tenantRepository.find({
      where: {
        key: dto.key,
      },
    });
    if (tenant.length) {
      const numSuggestions =
        Number(process.env.NUM_KEY_SUGGESTIONS) || DEFAULT_NUM_KEY_SUGGESTIONS;

      // Generate candidate keys in bulk
      const candidateKeys = Array.from(
        {length: numSuggestions * CANDIDATE_KEY_MULTIPLIER},
        (_, i) => `${dto.key}${i + 1}`,
      );

      // Check all in parallel
      const existingCounts = await Promise.all(
        candidateKeys.map(key =>
          this.tenantRepository
            .count({
              key: key,
            })
            .then(res => ({key, exists: res.count > 0})),
        ),
      );

      // Collect suggestions
      const suggestions = existingCounts
        .filter(res => !res.exists)
        .slice(0, numSuggestions)
        .map(res => res.key);

      return {
        available: false,
        suggestions,
      } as KeySuggestionDto;
    }
    return {
      available: true,
    } as KeySuggestionDto;
  }

  /**
   * Retrieves the count of tenants based on the provided filter.
   *
   * @remarks
   * This endpoint fetches the count of tenants that match the specified filter criteria.
   * It returns the count as a number.
   *
   * Requires `ViewTenant` permission and bearer token authentication.
   *
   * @param where - The filter criteria for querying tenants.
   * @returns The count of tenants matching the filter.
   *
   * @response 200 - The count of tenants matching the filter.
   *
   * @security BearerToken
   */
  @authorize({
    permissions: [PermissionKey.ViewTenant],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/count`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Tenant model count',
        content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
      },
    },
  })
  async count(@param.where(Tenant) where?: Where<Tenant>): Promise<Count> {
    return this.tenantRepository.count(where);
  }

  /**
   * Retrieves a list of tenants based on the provided filter.
   *
   * @remarks
   * This endpoint fetches a list of tenants that match the specified filter criteria.
   * It returns an array of tenant model instances.
   *
   * Requires `ViewTenant` permission and bearer token authentication.
   *
   * @param filter - The filter criteria for querying tenants.
   * @returns An array of `Tenant` instances matching the filter.
   *
   * @response 200 - Array of tenant model instances.
   *
   * @security BearerToken
   */
  @authorize({
    permissions: [PermissionKey.ViewTenant],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Tenant model instances',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(Tenant, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(@param.filter(Tenant) filter?: Filter<Tenant>): Promise<Tenant[]> {
    return this.tenantRepository.find(filter);
  }

  /**
   * Retrieves a tenant by its ID.
   *
   * @remarks
   * This endpoint fetches a specific tenant by its ID and includes related contact information,
   * uploaded files, and address details.
   * It returns the tenant model instance with all related data.
   *
   * Requires `ViewTenant` permission and bearer token authentication.
   *
   * @param id - The ID of the tenant to retrieve.
   * @returns A promise that resolves to the `Tenant` instance with contact details, files, and address.
   *
   * @response 200 - The tenant model instance with contact information, files, and address.
   *
   * @security BearerToken
   */
  @authorize({
    permissions: [PermissionKey.ViewTenant],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description:
          'Tenant model instance with contact details, files, and address',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(Tenant, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(@param.path.string('id') id: string): Promise<Tenant> {
    return this.tenantRepository.findById(id, {
      include: ['contacts', 'files', 'address'],
    });
  }

  /**
   * Retrieves all tenant statuses.
   *
   * @remarks
   * This endpoint fetches the current tenant statuses.
   *
   * Requires `ViewAllStatuses` permission.
   *
   * @returns An array of tenant statuses.
   *
   * @response 200 - Successfully retrieved tenant statuses.
   *
   * @security BearerToken
   */
  @authorize({
    permissions: [PermissionKey.ViewAllStatuses],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/all-status`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Object of all possible tenant status',
        permissions: [PermissionKey.ViewAllStatuses],
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'object',
              items: getModelSchemaRef(StatusDto, {
                title: 'TenantStatusDto',
              }),
            },
          },
        },
      },
    },
  })
  async findAllStatus(): Promise<StatusDto> {
    const statusMap = getTenantStatusMap();
    return new StatusDto({statuses: statusMap});
  }

  /**
   * Provisions a tenant with the given subscription details.
   *
   * @param subscription - The subscription details to provision the tenant.
   * @param id - The ID of the tenant to provision.
   * @returns A promise that resolves when the provisioning is complete.
   */
  @authorize({
    permissions: [PermissionKey.ProvisionTenant],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @post(`${basePath}/{id}/provision`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Provisioning success',
      },
    },
  })
  async provision(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(SubscriptionDTO, {
            title: 'SubscriptionDTO',
          }),
        },
      },
    })
    subscription: SubscriptionDTO,
    @param.path.string('id') id: string,
  ): Promise<void> {
    const tenantDetails = await this.tenantRepository.findById(id, {
      include: ['contacts', 'address'],
    });

    // Cast to the expected Tenant type to resolve enum mismatch
    return this.provisioningService.provisionTenant(
      tenantDetails as unknown as ProvisioningTenant,
      subscription,
    );
  }
}
