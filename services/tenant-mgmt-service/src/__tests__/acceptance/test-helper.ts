import {
  TenantManagementDbSourceName,
  TenantManagementCacheSourceName,
  ContactRepository,
  ResourceRepository,
} from '@sourceloop/ctrl-plane-tenant-management-service';
import {TenantMgmtServiceApplication} from '../..';
import {
  createRestAppClient,
  givenHttpServerConfig,
  Client,
} from '@loopback/testlab';
import {sign} from 'jsonwebtoken';
import {RestApplication} from '@loopback/rest/dist/rest.application';
import {AuthenticationBindings} from 'loopback4-authentication';
import {Context} from '@loopback/context';
import {AnyObject} from '@loopback/repository';
import {TenantRepository} from '../../repositories';
import {Transaction} from '../fixtures';

export async function setupApplication(): Promise<AppWithClient> {
  const restConfig = givenHttpServerConfig({
    // Customize the server configuration here.
    // Empty values (undefined, '') will be ignored by the helper.
    //
    // host: process.env.HOST,
    // port: +process.env.PORT,
  });
  setUpEnv();

  const app = new TenantMgmtServiceApplication({
    rest: restConfig,
  });

  app.bind(`datasources.config.${TenantManagementDbSourceName}`).to({
    name: TenantManagementDbSourceName,
    connector: 'memory',
  });

  app.bind(`datasources.config.${TenantManagementCacheSourceName}`).to({
    name: TenantManagementCacheSourceName,
    connector: 'kv-memory',
  });

  TenantRepository.prototype.beginTransaction = async () => new Transaction();
  ContactRepository.prototype.beginTransaction = async () => new Transaction();
  ResourceRepository.prototype.beginTransaction = async () => new Transaction();

  await app.boot();
  await app.start();

  const client = createRestAppClient(app);

  return {app, client};
}

function setUpEnv() {
  process.env.NODE_ENV = 'test';
  process.env.ENABLE_TRACING = '0';
  process.env.ENABLE_OBF = '0';
  process.env.REDIS_NAME = 'redis';
  process.env.HOST = 'localhost';

  process.env.JWT_ISSUER = 'test';
  process.env.JWT_SECRET = 'test';
}

export interface AppWithClient {
  app: TenantMgmtServiceApplication;
  client: Client;
}

export function getToken(permissions: string[] = [], withoutBearer = false) {
  return `${withoutBearer ? '' : 'Bearer '}${sign(
    {
      id: 'test',
      userTenantId: 'test',
      iss: process.env.JWT_ISSUER,
      permissions,
    },
    process.env.JWT_SECRET ?? '',
  )}`;
}

export async function getRepo<T>(app: RestApplication, classString: string) {
  const tempContext = new Context(app, 'test');
  tempContext.bind<AnyObject>(AuthenticationBindings.CURRENT_USER).to({
    id: 'test',
    username: 'test',
    userTenantId: 'test',
  });
  return tempContext.getSync<T>(classString);
}
