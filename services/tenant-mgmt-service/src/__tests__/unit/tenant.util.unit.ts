import {expect} from '@loopback/testlab';
import {getTenantStatusMap} from '../../util/tenant.util';
import {TenantStatus} from '@local/core';

describe('TenantStatus Utils', () => {
  describe('getTenantStatusMap()', () => {
    it('returns an object mapping numeric keys to status strings', () => {
      const statusMap = getTenantStatusMap();
      expect(statusMap).to.be.Object();
    });

    it('correctly maps all TenantStatus enum values', () => {
      const statusMap = getTenantStatusMap();

      // Test each enum value
      expect(statusMap[TenantStatus.ACTIVE]).to.equal('ACTIVE');
      expect(statusMap[TenantStatus.PENDINGPROVISION]).to.equal(
        'PENDINGPROVISION',
      );
      expect(statusMap[TenantStatus.PROVISIONING]).to.equal('PROVISIONING');
      expect(statusMap[TenantStatus.PROVISIONFAILED]).to.equal(
        'PROVISIONFAILED',
      );
      expect(statusMap[TenantStatus.INACTIVE]).to.equal('INACTIVE');
    });

    it('includes only numeric keys', () => {
      const statusMap = getTenantStatusMap();
      const keys = Object.keys(statusMap);

      // Verify all keys are numeric
      keys.forEach(key => {
        expect(Number.isInteger(Number(key))).to.be.true();
      });

      // Verify we have the expected number of statuses
      const six = 6;
      expect(keys.length).to.equal(six);
    });

    it('maps numeric keys to expected string values', () => {
      const statusMap = getTenantStatusMap();

      // Test specific numeric key to string mappings
      const zero = 0;
      const one = 1;
      const two = 2;
      const three = 3;
      const four = 4;
      expect(statusMap[zero]).to.equal('ACTIVE');
      expect(statusMap[one]).to.equal('PENDINGPROVISION');
      expect(statusMap[two]).to.equal('PROVISIONING');
      expect(statusMap[three]).to.equal('PROVISIONFAILED');
      expect(statusMap[four]).to.equal('INACTIVE');
    });
  });
});
