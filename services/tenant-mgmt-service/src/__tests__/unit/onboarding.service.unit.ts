import {expect, sinon} from '@loopback/testlab';
import {Contact, FileObject, Tenant, TenantOnboardDTO} from '../../models';
import {FileRepository, TenantRepository} from '../../repositories';
import {OnboardingService} from '../../services';
import {
  Address,
  AddressRepository,
  ContactRepository,
} from '@sourceloop/ctrl-plane-tenant-management-service';
import {StorageSource, TenantStatus} from '@local/core';
import {ILogger} from '@sourceloop/core';
import {Transaction} from '../fixtures';

describe('OnboardingService (unit)', () => {
  let onboardingService: OnboardingService;
  let tenantRepository: sinon.SinonStubbedInstance<TenantRepository>;

  let contactRepository: sinon.SinonStubbedInstance<ContactRepository<Contact>>;
  let addressRepository: sinon.SinonStubbedInstance<AddressRepository>;
  let fileRepository: sinon.SinonStubbedInstance<FileRepository>;
  let logger: sinon.SinonStubbedInstance<ILogger>;

  let transactionStub: sinon.SinonStubbedInstance<Transaction>;

  beforeEach(() => {
    tenantRepository = sinon.createStubInstance(TenantRepository);
    contactRepository = sinon.createStubInstance(ContactRepository);
    addressRepository = sinon.createStubInstance(AddressRepository);
    fileRepository = sinon.createStubInstance(FileRepository);

    transactionStub = sinon.createStubInstance(Transaction);

    // Set up stubbed methods
    transactionStub.commit.resolves();
    transactionStub.rollback.resolves();
    transactionStub.isActive.returns(true);

    (tenantRepository.beginTransaction as sinon.SinonStub).resolves(
      transactionStub,
    );

    logger = {
      log: sinon.stub(),
      info: sinon.stub(),
      warn: sinon.stub(),
      error: sinon.stub(),
      debug: sinon.stub(),
    } as unknown as sinon.SinonStubbedInstance<ILogger>;

    onboardingService = new OnboardingService(
      tenantRepository,
      contactRepository,
      addressRepository,
      fileRepository,
      logger,
    );
  });

  const dtoMock: TenantOnboardDTO = new TenantOnboardDTO({
    key: 'tenant-key',
    name: 'Test Tenant',
    domains: ['test.com'],
    country: 'IN',
    address: '123 Street',
    city: 'Delhi',
    state: 'DL',
    zip: '110001',
    lang: 'en',
    contact: new Contact({
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      isPrimary: true,
      countryCode: '+91',
      phoneNumber: '9999999999',
      designation: 'Manager',
    }),
    files: [
      new FileObject({
        fileKey: 'file1',
        originalName: 'test.pdf',
        source: StorageSource.S3,
        size: 200,
      }),
    ],
  });

  const tenantMock: Tenant = new Tenant({
    id: 'tenant123',
    key: dtoMock.key,
    name: dtoMock.name,
    domains: dtoMock.domains,
    status: TenantStatus.PENDINGPROVISION,
    addressId: 'address123',
    contacts: [],
    resources: [],
    lang: 'English',
    files: [],
  });

  const addressMock = {
    id: 'address123',
    address: '123 Street',
    city: 'Delhi',
    state: 'DL',
    country: 'IN',
    zip: '110001',
  };

  /**
   * ✅ Test 2: Successful onboarding without lead (creates new address)
   */
  it('creates a new address if no lead provided', async () => {
    addressRepository.create.resolves(addressMock as unknown as Address);
    tenantRepository.create.resolves(tenantMock);
    tenantRepository.findById.resolves(tenantMock);

    const res = await onboardingService.onboard(dtoMock);

    expect(addressRepository.create.calledOnce).to.be.true();
    expect(tenantRepository.create.calledOnce).to.be.true();
    expect(transactionStub.commit.calledOnce).to.be.true();
    expect(res).to.eql(tenantMock);
  });

  /**
   * ✅ Test 5: Rolls back if tenantRepository.create fails
   */
  it('rolls back transaction if tenantRepository.create throws', async () => {
    addressRepository.create.resolves(addressMock as unknown as Address);
    tenantRepository.create.rejects(new Error('DB error'));

    await expect(onboardingService.onboard(dtoMock)).to.be.rejectedWith(
      'DB error',
    );
    expect(transactionStub.rollback.calledOnce).to.be.true();
  });
});
