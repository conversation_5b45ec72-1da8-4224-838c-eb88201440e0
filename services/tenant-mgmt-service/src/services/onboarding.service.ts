import {TenantStatus} from '@local/core';
import {BindingScope, inject, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {ILogger, LOGGER} from '@sourceloop/core';
import {
  AddressRepository,
  ContactRepository,
} from '@sourceloop/ctrl-plane-tenant-management-service';
import {Contact, Tenant, TenantOnboardDTO} from '../models';
import {FileRepository, TenantRepository} from '../repositories';
const country = 'USA';
/**
 * Helper service for onboarding tenants.
 */
@injectable({scope: BindingScope.REQUEST})
export class OnboardingService {
  /**
   * Constructs a new instance of the OnboardingService.
   * @param {LeadRepository} leadRepository - Repository for managing leads.
   * @param {TenantRepository} tenantRepository - Repository for managing tenants.
   * @param {ContactRepository} contactRepository - Repository for managing contacts.
   * @param {AddressRepository} addressRepository - Repository for managing addresses.
   * @param {LeadAuthenticator} leadAuthenticator - Service for authenticating leads.
   * @param {ILogger} logger - Logger service for logging messages.
   */
  constructor(
    @repository(TenantRepository)
    private tenantRepository: TenantRepository<Tenant>,
    @repository(ContactRepository)
    private contactRepository: ContactRepository<Contact>,
    @repository(AddressRepository)
    private addressRepository: AddressRepository,
    @repository(FileRepository)
    private fileRepository: FileRepository,
    @inject(LOGGER.LOGGER_INJECT)
    private logger: ILogger,
  ) {}

  /**
   * The `setupTenant` function creates a new tenant with the provided information and an optional lead,
   * and returns the created tenant.
   * @param {TenantOnboardDTO} dto - The `dto` parameter is an object of type `TenantOnboardDTO` which
   * contains the necessary information to onboard a new tenant. It includes properties such as `key`,
   * `country`, `address`, `city`, `state`, `zip`, and `name`.
   * @param {Lead} [lead] - The `lead` parameter is an optional parameter of type `Lead`. It represents
   * the lead associated with the tenant being onboarded. If a lead is provided, their information will
   * be used to create a contact for the tenant. If no lead is provided, the contact will not be created.
   * @returns a Promise that resolves to a Tenant object.
   */
  async onboard(dto: TenantOnboardDTO): Promise<Tenant> {
    const existingContact = await this.contactRepository.findOne({
      where: {email: dto.contact.email},
    });
    if (existingContact) {
      throw new HttpErrors.BadRequest(
        `Contact with email ${dto.contact.email} already exists`,
      );
    }
    const transaction = await this.tenantRepository.beginTransaction();
    try {
      const address = await this.addressRepository.create(
        {
          country: dto.country ?? country,
          address: dto.address,
          city: dto.city,
          state: dto.state,
          zip: dto.zip,
        },
        {transaction},
      );

      const tenant = await this.tenantRepository.create(
        {
          key: dto.key,
          name: dto.name,
          domains: dto.domains,
          status: TenantStatus.PENDINGPROVISION,
          addressId: address?.id,
          lang: dto.lang,
        },
        {transaction},
      );

      await this.contactRepository.create(
        {
          email: dto.contact.email,
          firstName: dto.contact.firstName,
          lastName: dto.contact.lastName,
          tenantId: tenant.id,
          isPrimary: dto.contact.isPrimary,
          countryCode: dto.contact.countryCode,
          phoneNumber: dto.contact.phoneNumber,
          designation: dto.contact.designation,
        },
        {transaction},
      );

      if (dto.files && dto.files.length > 0) {
        await Promise.all(
          dto.files.map(async file => {
            await this.fileRepository.create(
              {
                fileKey: file.fileKey,
                originalName: file.originalName,
                tenantId: tenant.id,
                source: file.source,
                size: file.size,
              },
              {transaction},
            );
          }),
        );
      }

      const res = await this.tenantRepository.findById(
        tenant.id,
        {
          include: [
            {relation: 'contacts'},
            {relation: 'resources'},
            {relation: 'lead'},
            {relation: 'address'},
          ],
        },
        {transaction},
      );

      await transaction.commit();
      return res;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
}
