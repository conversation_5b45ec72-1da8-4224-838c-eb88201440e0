import {belongsTo, hasMany, model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {
  Resource,
  Address,
  Lead,
  numericEnumValues,
} from '@sourceloop/ctrl-plane-tenant-management-service';
import {File} from './file.model';
import {Contact} from './contact.model';
import {TenantStatus} from '@local/core';

@model({
  name: 'tenants',
  description:
    'main model of the service that represents a tenant in the system, either pooled or siloed',
})
export class Tenant extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id: string;

  @property({
    type: 'string',
    description: 'name of the tenant',
    required: true,
    jsonSchema: {
      minLength: 3,
      maxLength: 50,
      pattern: "^(?![-\\s])[a-zA-Z0-9\\s&.,'’-]+(?<![-\\s])$",
      errorMessage: {
        pattern:
          "Invalid name format. Use only letters, numbers, spaces, &, ., ,, ', and –. It cannot start or end with a space or hyphen.",
      },
    },
  })
  name: string;

  @property({
    type: 'number',
    description:
      'status of a tenant, it can be - 0(active), 1(provisioning),2(deprovisioning),3(inactive)',
    required: true,
    jsonSchema: {
      enum: numericEnumValues(TenantStatus),
    },
  })
  status: TenantStatus;

  @property({
    type: 'string',
    description:
      'A short string used to identify a tenant. This is also used as the namespace and subdomain for this particular tenant.',
    required: true,
    jsonSchema: {
      pattern: '^(?!-)[a-zA-Z0-9-]{3,63}(?<!-)$',
      minLength: 3,
      maxLength: 63,
    },
  })
  key: string;

  @property({
    name: 'spoc_user_id',
    description:
      'user id of the admin user who acts as a spoc for this tenant.',
    type: 'string',
  })
  spocUserId?: string;

  @property({
    type: 'array',
    itemType: 'string',
    description: 'array of domains that are allowed for this tenant',
    postgresql: {
      dataType: 'varchar[]',
    },
    required: true,
  })
  domains: string[];

  @hasMany(() => Contact, {
    keyTo: 'tenantId',
  })
  contacts: Contact[];

  @hasMany(() => Resource, {
    keyTo: 'tenantId',
  })
  resources: Resource[];

  @belongsTo(() => Lead, undefined, {
    description:
      'id of the lead from which this tenant was generated. this is optional as a tenant can be created without this lead.',
    name: 'lead_id',
  })
  leadId?: string;

  @belongsTo(() => Address, undefined, {
    name: 'address_id',
    description: 'id of the address of the tenant',
  })
  addressId: string;

  @property({
    type: 'string',
    name: 'lang',
  })
  lang: string;

  @hasMany(() => File, {keyTo: 'tenantId'})
  files: File[];

  constructor(data?: Partial<Tenant>) {
    super(data);
  }
}
