import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {Tenant} from './tenant.model';

const ONLY_LETTERS_AND_SPACES_REGEX = '^[a-zA-Z\\s]+$';
@model({
  name: 'contacts',
  description:
    'main model of the service that represents a tenant in the system, either pooled or siloed',
})
export class Contact extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    name: 'first_name',
    required: true,
    description: 'first name of the tenant admin',
    jsonSchema: {
      minLength: 3,
      maxLength: 50,
      pattern: ONLY_LETTERS_AND_SPACES_REGEX,
    },
  })
  firstName: string;

  @property({
    type: 'string',
    name: 'last_name',
    required: true,
    description: 'last name of the tenant admin',
    jsonSchema: {
      minLength: 3,
      maxLength: 50,
      pattern: ONLY_LETTERS_AND_SPACES_REGEX,
    },
  })
  lastName: string;

  @property({
    type: 'string',
    description: 'email id of the contact',
    jsonSchema: {
      format: 'email',
      pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,7}$',
      errorMessage: {
        pattern: 'Invalid email format. Please provide a valid email address.',
      },
    },
    required: true,
  })
  email: string;

  @property({
    type: 'string',
    name: 'designation',
    jsonSchema: {
      nullable: true,
      minLength: 3,
      maxLength: 50,
      pattern: ONLY_LETTERS_AND_SPACES_REGEX,
    },
  })
  designation?: string;

  @property({
    type: 'string',
    name: 'phone_number',
    jsonSchema: {
      nullable: true,
      minLength: 10,
      maxLength: 10,
      pattern: '^\\d{10}$',
      errorMessage: {
        pattern: 'Invalid phone number format. Use only digits.',
        minLength: 'Phone number must be at least 10 digits long.',
        maxLength: 'Phone number cannot exceed 10 characters.',
      },
    },
  })
  phoneNumber?: string;

  @property({
    type: 'string',
    name: 'country_code',
    jsonSchema: {
      minLength: 2,
      maxLength: 5,
      pattern: '^\\+[0-9]{1,4}$',
      errorMessage: {
        pattern:
          'Invalid country code format. It should start with an + followed by 1 to 4 digits.',
        minLength: 'Country code must be at least 2 characters.',
        maxLength: 'Country code cannot exceed 5 characters.',
      },
    },
  })
  countryCode: string;

  @property({
    name: 'is_primary',
    type: 'boolean',
    description:
      "boolean value denoting if the contact is a primary contact for it's tenant.",
    required: true,
  })
  isPrimary: boolean;

  @property({
    name: 'contact_type',
    type: 'string',
    description: 'type of the contact',
  })
  type?: string;

  @belongsTo(
    () => Tenant,
    {name: 'tenant'},
    {
      name: 'tenant_id',
      description: 'tenant id this contact belongs to',
    },
  )
  tenantId: string;

  constructor(data?: Partial<Contact>) {
    super(data);
  }
}
