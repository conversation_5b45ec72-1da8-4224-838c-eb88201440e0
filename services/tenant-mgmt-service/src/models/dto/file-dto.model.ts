import {StorageSource} from '@local/core';
import {Model, property} from '@loopback/repository';

export class FileObject extends Model {
  @property({type: 'string', required: true})
  fileKey: string;

  @property({type: 'string', required: true})
  originalName: string;

  @property({type: 'number', required: true})
  source: StorageSource;

  @property({type: 'number', required: true})
  size: number;

  constructor(data?: Partial<FileObject>) {
    super(data);
  }
}
