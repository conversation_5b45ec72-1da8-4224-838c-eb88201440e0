import {StorageSource} from '@local/core';
import {belongsTo, model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {Tenant} from './tenant.model';
@model({
  name: 'files',
})
export class File extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id: string;

  @belongsTo(
    () => Tenant,
    {
      keyTo: 'id',
    },
    {
      name: 'tenant_id',
    },
  )
  tenantId: string;

  @property({
    type: 'string',
    name: 'file_key',
  })
  fileKey: string;

  @property({
    type: 'string',
    name: 'original_name',
  })
  originalName: string;

  @property({
    type: 'number',
    name: 'source',
  })
  source: StorageSource;

  @property({
    type: 'number',
    name: 'size',
  })
  size: number;

  constructor(data?: Partial<File>) {
    super(data);
  }
}
