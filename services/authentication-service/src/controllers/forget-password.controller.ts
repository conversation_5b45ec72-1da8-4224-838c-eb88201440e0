﻿// Copyright (c) 2023 Sourcefuse Technologies
//
// This software is released under the MIT License.
// https://opensource.org/licenses/MIT

import {PermissionKey, UpdatePasswordDto} from '@local/core';
import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors, getModelSchemaRef, post, requestBody} from '@loopback/rest';
import {
  AuthClientRepository,
  UserCredentialsRepository,
} from '@sourceloop/authentication-service';
import {
  CONTENT_TYPE,
  ErrorCodes,
  IAuthUserWithPermissions,
  ILogger,
  LOGGER,
  OPERATION_SECURITY_SPEC,
  STATUS_CODE,
} from '@sourceloop/core';
import * as bcrypt from 'bcrypt';
import {
  AuthenticationBindings,
  STRATEGY,
  authenticate,
} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';

const basePath = 'auth/forget-password';
const saltRounds = 10;

/**
 * Controller to handle forget password verification and update operations.
 */
export class ForgetPasswordController {
  /**
   * Constructor to inject necessary dependencies.
   * @param userCredentialsRepository - Repository for user credentials.
   * @param authClientRepo - Repository for authentication clients.
   * @param logger - Logger instance.
   * @param currentUser - The currently authenticated user.
   */
  constructor(
    @repository(UserCredentialsRepository)
    private readonly userCredentialsRepository: UserCredentialsRepository,

    @repository(AuthClientRepository)
    private readonly authClientRepo: AuthClientRepository,

    @inject(LOGGER.LOGGER_INJECT)
    private logger: ILogger,

    @inject(AuthenticationBindings.CURRENT_USER)
    private readonly currentUser: IAuthUserWithPermissions,
  ) {}

  /**
   * Endpoint to verify forget-password token and update the user's password.
   *
   * @param dto - DTO containing new password and client credentials.
   * @throws {HttpErrors.BadRequest} If clientId or clientSecret is missing.
   * @throws {HttpErrors.Unauthorized} If provided client credentials are invalid.
   * @throws {HttpErrors.NotFound} If user credentials are not found.
   *
   * @returns {Promise<void>} A successful password update returns no content.
   */
  @authorize({
    permissions: [PermissionKey.UpdatePassword],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @post(`${basePath}/verify`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Success Response.',
      },
      ...ErrorCodes,
    },
  })
  async updatePassword(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(UpdatePasswordDto),
        },
      },
    })
    dto: UpdatePasswordDto,
  ): Promise<void> {
    if (!dto.clientId && !dto.clientSecret) {
      throw new HttpErrors.BadRequest(
        'Client ID and Client Secret are required',
      );
    }

    const authClient = await this.authClientRepo.findOne({
      where: {
        clientId: dto.clientId,
        clientSecret: dto.clientSecret,
      },
    });

    if (!authClient) {
      this.logger.error(`Auth client with ID ${dto.clientId} does not exist`);
      throw new HttpErrors.Unauthorized('Invalid client credentials');
    }

    const credentials = await this.userCredentialsRepository.findOne({
      where: {userId: this.currentUser.id},
    });

    if (!credentials) {
      throw new HttpErrors.NotFound('User credentials not found');
    }

    const hashedPassword = await bcrypt.hash(dto.newPassword, saltRounds);

    await this.userCredentialsRepository.updateById(credentials.id, {
      password: hashedPassword,
    });
  }
}
