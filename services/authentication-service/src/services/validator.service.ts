import {BindingScope, injectable} from '@loopback/context';
import {HttpErrors} from '@loopback/rest';
import {PUBLIC_DOMAINS} from '../constant';

@injectable({scope: BindingScope.TRANSIENT})
export class ValidatorService {
  constructor() {}

  /**
   * The function `validate` checks if an email domain is in a list of public domains and throws an error
   * if it is.
   * @param {string} email - The `validate` function takes an email address as a parameter and checks if
   * the domain of the email address is included in the `PUBLIC_DOMAINS` array. If the domain is found in
   * the `PUBLIC_DOMAINS` array, it throws an error with the message `PUBLIC_DOMAIN_EMAIL`.
   */
  validateEmail(email: string) {
    const domain = email.split('@')[1];
    if (PUBLIC_DOMAINS.includes(domain)) {
      throw new HttpErrors.BadRequest('Public domain email is not allowed.');
    }
  }
}
