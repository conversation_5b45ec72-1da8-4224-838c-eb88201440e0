import {Context} from '@loopback/context';
import {AnyObject} from '@loopback/repository';
import {RestApplication} from '@loopback/rest';
import {AuthenticationServiceApplication} from '../..';
import {
  createRestAppClient,
  givenHttpServerConfig,
  Client,
} from '@loopback/testlab';
import {
  AuthCacheSourceName,
  AuthDbSourceName,
  AuthenticationBindings,
} from '@sourceloop/authentication-service';
import {sign} from 'jsonwebtoken';
import {mockUser} from './mock-data';
export async function setupApplication(): Promise<AppWithClient> {
  const restConfig = givenHttpServerConfig({
    // Customize the server configuration here.
    // Empty values (undefined, '') will be ignored by the helper.
    //
    // host: process.env.HOST,
    // port: +process.env.PORT,
  });
  setUpEnv();

  const app = new AuthenticationServiceApplication({
    rest: restConfig,
  });

  app.bind(`datasources.config.${AuthDbSourceName}`).to({
    name: AuthDbSourceName,
    connector: 'sqlite3',
    file: ':memory:',
  });

  app.bind(`datasources.config.${AuthCacheSourceName}`).to({
    name: AuthCacheSourceName,
    connector: 'kv-memory',
  });

  await app.boot();
  await app.start();

  const client = createRestAppClient(app);

  return {app, client};
}

function setUpEnv() {
  process.env.NODE_ENV = 'test';
  process.env.ENABLE_TRACING = '0';
  process.env.ENABLE_OBF = '0';
  process.env.JWT_ISSUER = 'test';
  process.env.JWT_SECRET = '6356345364';
  process.env.LEAD_TOKEN_EXPIRY = '30000';
  process.env.WEBHOOK_SECRET_EXPIRY = '30000';
  process.env.VALIDATION_TOKEN_EXPIRY = '30000';
  process.env.LEAD_KEY_LENGTH = '4';
  process.env.REDIS_NAME = 'redis';
  process.env.HOST = 'localhost';
}

export interface AppWithClient {
  app: AuthenticationServiceApplication;
  client: Client;
}
export function getToken(permissions: string[] = [], withoutBearer = false) {
  return `${withoutBearer ? '' : 'Bearer '}${sign(
    {
      id: 'test',
      userTenantId: 'test',
      iss: process.env.JWT_ISSUER,
      permissions,
    },
    process.env.JWT_SECRET ?? '',
  )}`;
}

export async function getRepo<T>(app: RestApplication, classString: string) {
  const tempContext = new Context(app, 'test');
  tempContext.bind<AnyObject>(AuthenticationBindings.CURRENT_USER).to({
    id: mockUser.id,
    username: mockUser.username,
    userTenantId: mockUser.userTenantId,
  });
  return tempContext.getSync<T>(classString);
}
