import {Per<PERSON><PERSON><PERSON>} from '@local/core';
import {BindingScope} from '@loopback/context';
import {Client} from '@loopback/testlab';
import {
  AuthClient,
  AuthClientRepository,
  UserCredentials,
  UserCredentialsRepository,
} from '@sourceloop/authentication-service';
import {ILogger, LOGGER, STATUS_CODE} from '@sourceloop/core';
import sinon from 'sinon';
import {AuthenticationServiceApplication} from '../../application';
import {
  mockAuthClient,
  mockCredentials,
  mockUpdatePasswordDto,
} from './mock-data';
import {getToken, setupApplication} from './test-helper';

describe('forgetPasswordController', () => {
  let app: AuthenticationServiceApplication;

  let client: Client;
  let userCredentialsRepositoryStub: sinon.SinonStubbedInstance<UserCredentialsRepository>;
  let authClientRepoStub: sinon.SinonStubbedInstance<AuthClientRepository>;
  before('setupApplication', async () => {
    ({app, client} = await setupApplication());

    const logger = app.getSync<ILogger>(LOGGER.LOGGER_INJECT);
    app.bind(LOGGER.LOGGER_INJECT).to(logger).inScope(BindingScope.SINGLETON);
  });

  after(async () => {
    await app.stop();
  });
  beforeEach(async () => {
    // userCredentialsRepositoryStub = sinon.createStubInstance(UserCredentialsRepository);
    // Seed data if necessary
    userCredentialsRepositoryStub = {
      findOne: sinon
        .stub<
          [
            filter?: import('@loopback/repository').Filter<UserCredentials>,
            options?: import('@loopback/repository').Options,
          ],
          Promise<UserCredentials>
        >()
        .resolves(mockCredentials),
      updateById: sinon
        .stub<
          [
            id: string,
            data: Partial<UserCredentials>,
            options?: import('@loopback/repository').Options,
          ],
          Promise<void>
        >()
        .resolves(),
    } as unknown as sinon.SinonStubbedInstance<UserCredentialsRepository>;
    authClientRepoStub = {
      findOne: sinon
        .stub<
          [
            filter?: import('@loopback/repository').Filter<AuthClient>,
            options?: import('@loopback/repository').Options,
          ],
          Promise<AuthClient>
        >()
        .resolves(mockAuthClient),
    } as unknown as sinon.SinonStubbedInstance<AuthClientRepository>;

    app
      .bind('repositories.UserCredentialsRepository')
      .to(userCredentialsRepositoryStub);
    app.bind('repositories.AuthClientRepository').to(authClientRepoStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  it('invokes POST /auth/forget-password/verify with valid token', async () => {
    const token = getToken([PermissionKey.UpdatePassword]);
    const dto = {...mockUpdatePasswordDto};
    await client
      .post('/auth/forget-password/verify')
      .set('Authorization', token)
      .send(dto)
      .expect(STATUS_CODE.NO_CONTENT);
  });
});
