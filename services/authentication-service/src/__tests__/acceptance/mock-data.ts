import {AuthClient, UserCredentials} from '@sourceloop/authentication-service';
/* eslint-disable @typescript-eslint/naming-convention */

export const mockAuthClient: AuthClient = new AuthClient({
  id: 1,
  client_id: 'test-client-id',
  client_secret: 'test-secret',
});

export const mockCredentials: UserCredentials = new UserCredentials({
  id: 'test-credentials-id',
  userId: 'test-user-id',
  password: 'test-password',
});

export const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  username: 'testuser',
  userTenantId: 'test-tenant-id',
};

export const mockUpdatePasswordDto = {
  newPassword: 'new-password',
  clientId: 'test-client-id',
  clientSecret: 'test-secret',
};
