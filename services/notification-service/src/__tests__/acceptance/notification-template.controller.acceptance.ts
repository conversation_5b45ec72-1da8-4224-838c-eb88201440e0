import sinon from 'sinon';
import {NotificationServiceApplication} from '../../application';
import {Client, expect} from '@loopback/testlab';
import {NotificationTemplatesRepository} from '../../repositories';
import {getToken, setupApplication} from './test-helper';
import {mockNotificationTemplate} from './mock-data';
import {NotificationTemplates, PermissionKey} from '@local/core';
import {STATUS_CODE} from '@sourceloop/core';

describe('NotificationTemplatesController', () => {
  let app: NotificationServiceApplication;

  let client: Client;
  let notificationTemplatesRepository: sinon.SinonStubbedInstance<NotificationTemplatesRepository>;
  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
  });

  after(async () => {
    await app.stop();
  });
  beforeEach(async () => {
    // Seed data if necessary
    notificationTemplatesRepository = {
      findOne: sinon
        .stub<
          [
            filter?: import('@loopback/repository').Filter<NotificationTemplates>,
            options?: import('@loopback/repository').Options,
          ],
          Promise<NotificationTemplates>
        >()
        .resolves(mockNotificationTemplate),
    } as unknown as sinon.SinonStubbedInstance<NotificationTemplatesRepository>;

    app
      .bind('repositories.NotificationTemplatesRepository')
      .to(notificationTemplatesRepository);
  });

  afterEach(() => {
    sinon.restore();
  });

  it('invokes GET /notification-templates/{event_name}/{notification_type} with valid token', async () => {
    const token = getToken([PermissionKey.ViewNotificationTemplate]);
    const {body} = await client
      .get(
        `/notification-templates/${mockNotificationTemplate.eventName}/${mockNotificationTemplate.notificationType}`,
      )
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);

    expect(body).to.have.property('id', mockNotificationTemplate.id);
    expect(body).to.have.property(
      'eventName',
      mockNotificationTemplate.eventName,
    );
    expect(body).to.have.property(
      'notificationType',
      mockNotificationTemplate.notificationType,
    );
    expect(body).to.have.property('subject', mockNotificationTemplate.subject);
    expect(body).to.have.property('body', mockNotificationTemplate.body);
  });
});
