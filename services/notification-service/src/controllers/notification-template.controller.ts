import {repository} from '@loopback/repository';
import {get, getModelSchemaRef, param} from '@loopback/rest';
import {
  CONTENT_TYPE,
  OPERATION_SECURITY_SPEC,
  STATUS_CODE,
} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {NotificationTemplatesRepository} from '../repositories';
import {NotificationTemplates, PermissionKey} from '@local/core';

/**
 * Controller for handling Notification Template related APIs.
 */
export class NotificationTemplatesController {
  /**
   * Creates an instance of NotificationTemplatesController.
   * @param notificationTemplatesRepository - The NotificationTemplates repository instance.
   */
  constructor(
    @repository(NotificationTemplatesRepository)
    private readonly notificationTemplatesRepository: NotificationTemplatesRepository,
  ) {}

  /**
   * Retrieves a notification template based on the event name and notification type.
   *
   * @param eventName - The name of the event to filter the template.
   * @param notificationType - The type of the notification to filter the template.
   * @returns A Promise that resolves to a NotificationTemplates instance or null if not found.
   *
   * @authentication Bearer token authentication is required.
   * @authorization Requires 'ViewNotificationTemplate' permission.
   */
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @authorize({permissions: [PermissionKey.ViewNotificationTemplate]})
  @get(`/notification-templates/{event_name}/{notification_type}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'NotificationTemplates model instance',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(NotificationTemplates),
          },
        },
      },
    },
  })
  async getNotificationTemplates(
    @param.path.string('event_name') eventName: string,
    @param.path.string('notification_type') notificationType: number,
  ): Promise<NotificationTemplates | null> {
    return this.notificationTemplatesRepository.findOne({
      where: {eventName: eventName, notificationType: notificationType},
    });
  }
}
