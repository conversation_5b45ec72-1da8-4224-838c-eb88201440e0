import {Getter, inject} from '@loopback/core';
import {
  DefaultUserModifyCrudRepository,
  IAuthUserWithPermissions,
} from '@sourceloop/core';
import {AuthenticationBindings} from 'loopback4-authentication';
import {NotifDbSourceName} from '@sourceloop/notification-service';
import {NotifDataSource} from '../datasources';
import {
  NotificationTemplates,
  NotificationTemplatesRelations,
} from '@local/core';

/**
 * Repository class for handling CRUD operations on NotificationTemplates.
 * Extends DefaultUserModifyCrudRepository to automatically handle
 * user-modified audit fields.
 */
export class NotificationTemplatesRepository extends DefaultUserModifyCrudRepository<
  NotificationTemplates,
  typeof NotificationTemplates.prototype.id,
  NotificationTemplatesRelations
> {
  /**
   * Creates an instance of NotificationTemplatesRepository.
   *
   * @param {NotifDataSource} dataSource - The data source instance injected using the datasource binding key.
   * @param {Getter<IAuthUserWithPermissions>} getCurrentUser - A getter function to access the current authenticated user and their permissions.
   */
  constructor(
    @inject(`datasources.${NotifDbSourceName}`) dataSource: NotifDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
  ) {
    super(NotificationTemplates, dataSource, getCurrentUser);
  }
}
