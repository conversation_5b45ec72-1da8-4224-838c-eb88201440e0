import {expect} from '@loopback/testlab';
import {RoleViewController} from '../../../controllers/role-view.controller';
import {RoleViewRepository} from '../../../repositories';
import {Filter, Count, Where} from '@loopback/repository';
import {RoleView} from '@local/core';
import sinon from 'sinon';

describe('RoleViewController (unit)', () => {
  let controller: RoleViewController;
  let roleViewRepository: sinon.SinonStubbedInstance<RoleViewRepository>;

  beforeEach(() => {
    roleViewRepository = sinon.createStubInstance(RoleViewRepository);
    controller = new RoleViewController(roleViewRepository);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('find', () => {
    it('returns array of tenant user views with combined filter', async () => {
      const testId = 'dummy-tenant-id';
      const mockFilter: Filter<RoleView> = {
        where: {
          tenantId: 'some-other-id',
        },
      };

      const expectedFilter: Filter<RoleView> = {
        where: {
          and: [{tenantId: testId}, {tenantId: 'some-other-id'}],
        },
      };

      const mockResult = [
        {
          roleId: '1',
          tenantId: testId,
        } as RoleView,
      ];

      roleViewRepository.find.resolves(mockResult);

      const result = await controller.find(testId, mockFilter);

      expect(result).to.deepEqual(mockResult);
      sinon.assert.calledWith(roleViewRepository.find, expectedFilter);
    });

    it('returns array of tenant user views with tenant filter only when no additional filter provided', async () => {
      const testId = 'dummy-tenant-id';
      const expectedFilter: Filter<RoleView> = {
        where: {
          and: [{tenantId: testId}],
        },
      };

      const mockResult = [
        {
          roleId: '1',
          tenantId: testId,
        } as RoleView,
      ];

      roleViewRepository.find.resolves(mockResult);

      const result = await controller.find(testId);

      expect(result).to.deepEqual(mockResult);
      sinon.assert.calledWith(roleViewRepository.find, expectedFilter);
    });
  });

  describe('count', () => {
    it('returns count with combined where clause', async () => {
      const testId = 'dummy-tenant-id';
      const mockWhere: Where<RoleView> = {
        roleId: '123',
      };

      const expectedWhere: Where<RoleView> = {
        tenantId: testId,
        roleId: '123',
      };

      const mockCount: Count = {
        count: 5,
      };

      roleViewRepository.count.resolves(mockCount);

      const result = await controller.count(testId, mockWhere);

      expect(result).to.deepEqual(mockCount);
      sinon.assert.calledWith(roleViewRepository.count, expectedWhere);
    });

    it('returns count with tenant filter only when no where clause provided', async () => {
      const testId = 'dummy-tenant-id';
      const expectedWhere: Where<RoleView> = {
        tenantId: testId,
      };

      const mockCount: Count = {
        count: 3,
      };

      roleViewRepository.count.resolves(mockCount);

      const result = await controller.count(testId);

      expect(result).to.deepEqual(mockCount);
      sinon.assert.calledWith(roleViewRepository.count, expectedWhere);
    });
  });
});
