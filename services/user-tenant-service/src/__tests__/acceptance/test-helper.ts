import {AuthDbSourceName} from '@sourceloop/core';
import {UserTenantServiceApplication} from '../..';
import {
  createRestAppClient,
  givenHttpServerConfig,
  Client,
} from '@loopback/testlab';
import {sign} from 'jsonwebtoken';
import {RestApplication} from '@loopback/rest/dist/rest.application';
import {Context} from '@loopback/context';
import {AuthenticationBindings} from 'loopback4-authentication';
import {AnyObject} from '@loopback/repository';

export async function setupApplication(): Promise<AppWithClient> {
  const restConfig = givenHttpServerConfig({
    // Customize the server configuration here.
    // Empty values (undefined, '') will be ignored by the helper.
    //
    // host: process.env.HOST,
    // port: +process.env.PORT,
  });
  setUpEnv();

  const app = new UserTenantServiceApplication({
    rest: restConfig,
  });

  app.bind(`datasources.config.${AuthDbSourceName}`).to({
    name: AuthDbSourceName,
    connector: 'sqlite3',
    file: ':memory:',
    // autoSchema: true,
  });

  await app.boot();
  await app.start();

  const client = createRestAppClient(app);

  return {app, client};
}

function setUpEnv() {
  process.env.NODE_ENV = 'test';
  process.env.ENABLE_TRACING = '0';
  process.env.ENABLE_OBF = '0';
  process.env.REDIS_NAME = 'redis';
  process.env.HOST = 'localhost';

  process.env.JWT_ISSUER = 'test';
  process.env.JWT_SECRET = 'test';
}

export interface AppWithClient {
  app: UserTenantServiceApplication;
  client: Client;
}

export const defaultTestTenantId = '3f09f2e5-b6e7-4e5d-bb71-bf3fcb7a1f69';

export function getToken(permissions: string[] = [], withoutBearer = false) {
  return `${withoutBearer ? '' : 'Bearer '}${sign(
    {
      id: 'test',
      userTenantId: 'test',
      iss: process.env.JWT_ISSUER,
      permissions,
      tenantId: '3f09f2e5-b6e7-4e5d-bb71-bf3fcb7a1f69',
    },
    process.env.JWT_SECRET ?? '',
  )}`;
}

export async function getRepo<T>(app: RestApplication, classString: string) {
  const tempContext = new Context(app, 'testC');
  tempContext.bind<AnyObject>(AuthenticationBindings.CURRENT_USER).to({
    id: 'test',
    username: 'test',
    userTenantId: 'test',
    tenantId: '3f09f2e5-b6e7-4e5d-bb71-bf3fcb7a1f69',
  });
  return tempContext.getSync<T>(classString);
}
