import {Client, expect, sinon} from '@loopback/testlab';
import {getToken, setupApplication} from './test-helper';
import {UserTenantServiceApplication} from '../..';

import {Filter, Where, Count} from '@loopback/repository';
import {RoleView, PermissionKey} from '@local/core';
import {RoleViewRepository} from '../../repositories';

describe('RoleViewController Acceptance', () => {
  let app: UserTenantServiceApplication;
  let client: Client;
  let roleViewRepositoryStub: Partial<RoleViewRepository>;

  const testTenantId = '3f09f2e5-b6e7-4e5d-bb71-bf3fcb7a1f69';
  const mockRoleView = {
    tenantId: testTenantId,
    roleId: 'role-123',
    roleName: 'admin',
  };

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());

    // Create a stubbed version of RoleViewRepository
    roleViewRepositoryStub = {
      find: sinon.stub().resolves([mockRoleView]),
      count: sinon.stub().resolves({count: 1} as Count),
    };

    // Bind stubbed repository to override the real one
    app.bind('repositories.RoleViewRepository').to(roleViewRepositoryStub);
  });

  after(async () => {
    await app.stop();
  });

  describe('GET /tenants/{id}/role-view', () => {
    it('should return roles when called with valid token and permissions', async () => {
      const filter: Filter<RoleView> = {
        where: {
          roleId: 'role-123',
        },
      };

      const res = await client
        .get(`/tenants/${testTenantId}/role-view`)
        .query({
          filter: JSON.stringify(filter),
        })
        .set('Authorization', getToken([PermissionKey.ViewRoles]))
        .expect(200);

      expect(res.body).to.be.an.Array();
      expect(res.body[0]).to.containEql(mockRoleView);

      const stub = roleViewRepositoryStub.find as sinon.SinonStub;
      sinon.assert.calledOnce(stub);
      expect(stub.firstCall.args[0]).to.containDeep({
        where: {
          and: [{tenantId: testTenantId}, {roleId: 'role-123'}],
        },
      });
    });

    it('should return roles when called without filter', async () => {
      const res = await client
        .get(`/tenants/${testTenantId}/role-view`)
        .set('Authorization', getToken([PermissionKey.ViewRoles]))
        .expect(200);

      expect(res.body).to.be.an.Array();
      expect(res.body[0]).to.containEql(mockRoleView);

      const stub = roleViewRepositoryStub.find as sinon.SinonStub;
      sinon.assert.calledWith(stub, {
        where: {
          and: [{tenantId: testTenantId}],
        },
      });
    });
  });

  describe('GET /tenants/{id}/role-view/count', () => {
    it('should return count when called with valid token and permissions', async () => {
      const where: Where<RoleView> = {
        roleId: 'role-123',
      };

      const res = await client
        .get(`/tenants/${testTenantId}/role-view/count`)
        .query({where})
        .set('Authorization', getToken([PermissionKey.ViewRoles]))
        .expect(200);

      expect(res.body).to.containEql({count: 1});

      const stub = roleViewRepositoryStub.count as sinon.SinonStub;
      sinon.assert.calledOnce(stub);
      expect(stub.firstCall.args[0]).to.containEql({
        tenantId: testTenantId,
        roleId: 'role-123',
      });
    });

    it('should return count when called without where clause', async () => {
      const res = await client
        .get(`/tenants/${testTenantId}/role-view/count`)
        .set('Authorization', getToken([PermissionKey.ViewRoles]))
        .expect(200);

      expect(res.body).to.containEql({count: 1});

      const stub = roleViewRepositoryStub.count as sinon.SinonStub;
      sinon.assert.calledWith(stub, {
        tenantId: testTenantId,
      });
    });
  });

  describe('Authentication and Authorization', () => {
    it('should reject requests without authorization token', async () => {
      await client.get(`/tenants/${testTenantId}/role-view`).expect(401);
      await client.get(`/tenants/${testTenantId}/role-view/count`).expect(401);
    });

    it('should reject requests with invalid permissions', async () => {
      const token = getToken([]);
      await client
        .get(`/tenants/${testTenantId}/role-view`)
        .set('Authorization', token)
        .expect(403);

      await client
        .get(`/tenants/${testTenantId}/role-view/count`)
        .set('Authorization', token)
        .expect(403);
    });
  });
});
