import {Client, expect} from '@loopback/testlab';
import {UserTenantServiceApplication} from '../..';
import {setupApplication, getToken} from './test-helper';
import {PermissionKey} from '@local/core';
import {UserViewRepository} from '@sourceloop/user-tenant-service';
import sinon from 'sinon';

describe('TenantUserV2Controller (acceptance)', () => {
  let app: UserTenantServiceApplication;
  let client: Client;
  let userViewRepositoryStub: sinon.SinonStubbedInstance<UserViewRepository>;

  const tenantId = '3f09f2e5-b6e7-4e5d-bb71-bf3fcb7a1f69';

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
  });

  after(async () => {
    sinon.restore();
    await app.stop();
  });

  beforeEach(() => {
    sinon.restore();
    userViewRepositoryStub = sinon.createStubInstance(UserViewRepository);
    userViewRepositoryStub.count.resolves({count: 2});
    app.bind('repositories.UserViewRepository').to(userViewRepositoryStub);
  });

  it('invokes GET /tenants/{id}/users/count and returns correct user count (ViewTenantUser)', async () => {
    userViewRepositoryStub.count.resolves({count: 2});
    const token = getToken([PermissionKey.ViewTenantUser]);
    const res = await client
      .get(`/tenants/${tenantId}/users/count`)
      .set('Authorization', token)
      .expect(200);
    expect(res.body).to.have.property('count', 2);
  });

  it('invokes GET /tenants/{id}/users/count for tenant with no users returns 0 (ViewTenantUser)', async () => {
    userViewRepositoryStub.count.resolves({count: 0});
    const token = getToken([PermissionKey.ViewTenantUser]);
    const res = await client
      .get(`/tenants/${tenantId}/users/count`)
      .set('Authorization', token)
      .expect(200);
    expect(res.body).to.have.property('count', 0);
  });

  it('returns 401 for GET /tenants/{id}/users/count without token', async () => {
    await client.get(`/tenants/${tenantId}/users/count`).expect(401);
  });

  it('returns 401 for GET /tenants/{id}/users/count without authorization token', async () => {
    await client.get(`/tenants/${tenantId}/users/count`).expect(401);
  });

  it('returns 403 for GET /tenants/{id}/users/count with invalid permissions', async () => {
    const token = getToken([]);
    await client
      .get(`/tenants/${tenantId}/users/count`)
      .set('Authorization', token)
      .expect(403);
  });
});
