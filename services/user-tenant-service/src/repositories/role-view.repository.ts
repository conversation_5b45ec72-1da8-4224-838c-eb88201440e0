import {inject} from '@loopback/core';

import {AuthDataSource} from '../datasources';
import {SequelizeCrudRepository} from '@loopback/sequelize';
import {RoleView} from '@local/core';
import {AuthDbSourceName} from '@sourceloop/core';

/**
 * Repository for managing tenant user roles.
 * This repository provides access to the tenant user role data.
 */
export class RoleViewRepository extends SequelizeCrudRepository<
  RoleView,
  typeof RoleView.prototype.roleId
> {
  constructor(
    @inject(`datasources.${AuthDbSourceName}`)
    dataSource: AuthDataSource,
  ) {
    super(RoleView, dataSource);
  }
}
