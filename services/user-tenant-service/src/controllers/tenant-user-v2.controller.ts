import {Permission<PERSON><PERSON>} from '@local/core';
import {intercept} from '@loopback/core';
import {Count, CountSchema, repository, Where} from '@loopback/repository';
import {get, param} from '@loopback/rest';
import {
  CONTENT_TYPE,
  OPERATION_SECURITY_SPEC,
  STATUS_CODE,
} from '@sourceloop/core';
import {
  UserTenantServiceKey,
  UserView,
  UserViewRepository,
} from '@sourceloop/user-tenant-service';
import {STRATEGY, authenticate} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
const baseUrl = '/tenants/{id}/users';

/**
 * Controller responsible for handling user count operations for a specific tenant.
 *
 * @remarks
 * This controller provides endpoints to retrieve the count of users associated with a tenant.
 */
@intercept(UserTenantServiceKey.TenantInterceptorInterceptor)
export class TenantUserV2Controller {
  /**
   * Creates an instance of TenantUserV2Controller.
   * @param userTenantRepository - The repository for user-tenant relations.
   */
  constructor(
    @repository(UserViewRepository)
    readonly userViewRepository: UserViewRepository,
  ) {}

  /**
   * Retrieves the count of users for a given tenant.
   *
   * @remarks
   * This endpoint returns the number of users associated with the specified tenant ID.
   * Requires bearer token authentication and appropriate permissions.
   *
   * @param id - The ID of the tenant.
   * @returns The count of users for the tenant.
   *
   * @response 200 - Successfully retrieved user count.
   * @security BearerToken
   */
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @authorize({
    permissions: [PermissionKey.ViewTenantUser],
  })
  @get(`${baseUrl}/count`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Object containing user count',
        content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.path.string('id') id: string,
    @param.where(UserView)
    where?: Where<UserView>,
  ): Promise<Count> {
    const whereObj = {
      ...where,
      tenantId: id,
    };
    return this.userViewRepository.count(whereObj);
  }
}
