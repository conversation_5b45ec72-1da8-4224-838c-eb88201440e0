import {Count, CountSchema, Filter, Where} from '@loopback/repository';
import {get, getModelSchemaRef, param} from '@loopback/rest';
import {RoleViewRepository} from '../repositories';
import {authorize} from 'loopback4-authorization';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {OPERATION_SECURITY_SPEC} from '@sourceloop/core';
import {PermissionKey, RoleView} from '@local/core';
import {inject} from '@loopback/context';

/**
 * Controller for managing tenant user roles.
 * This controller provides endpoints for retrieving and managing user roles within a tenant.
 */
export class RoleViewController {
  constructor(
    @inject('repositories.RoleViewRepository')
    public tenantUserViewRepository: RoleViewRepository,
  ) {}

  /**
   * Get roles by tenant user group
   * @param id
   * @param filter
   * @returns
   */

  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @authorize({
    permissions: [PermissionKey.ViewRoles],
  })
  @get('/tenants/{id}/role-view', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'Array of TenantUserView model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(RoleView),
            },
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.filter(RoleView) filter?: Filter<RoleView>,
  ): Promise<RoleView[]> {
    const combinedFilter: Filter<RoleView> = {
      ...filter,
      where: {
        and: [{tenantId: id}, ...(filter?.where ? [filter.where] : [])],
      },
    };

    return this.tenantUserViewRepository.find(combinedFilter);
  }

  /**
   * Get roles by tenant user group
   * @param id
   * @param where
   * @returns
   */

  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @authorize({
    permissions: [PermissionKey.ViewRoles],
  })
  @get('/tenants/{id}/role-view/count', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'TenantUserView model count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.path.string('id') id: string,
    @param.where(RoleView) where?: Where<RoleView>,
  ): Promise<Count> {
    const whereBuilder: Where<RoleView> = {
      tenantId: id,
      ...where,
    };
    return this.tenantUserViewRepository.count(whereBuilder);
  }
}
