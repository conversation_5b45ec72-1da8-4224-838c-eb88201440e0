#!/bin/bash

# This script scans for vulnerabilities in changed files.
# It can scan for uncommitted changes or changes since a specific commit.

set -euo pipefail

if [[ -n "${1:-}" ]]; then
  # Check if the provided commit hash exists in the repository
  if ! git cat-file -e "$1" 2>/dev/null; then
    echo "Base commit hash ($1) does not exist. Scanning all tracked files..."
    # Get all tracked files that are relevant for security scanning
    changed_files=$(git ls-files | grep -E '\.(js|ts|json|yaml|yml|dockerfile|Dockerfile)$' | head -100)
  else
    echo "Scanning for files changed since commit $1..."
    changed_files=$(git diff --name-only "$1" HEAD)
  fi
else
  echo "No base commit provided. Scanning for uncommitted file changes..."
  changed_files=$(git diff --name-only)
  # If no uncommitted changes, scan recently changed files
  if [[ -z "$changed_files" ]]; then
    echo "No uncommitted changes found. Scanning files from last commit..."
    changed_files=$(git diff --name-only HEAD~1 HEAD)
  fi
fi

if [[ -z "$changed_files" ]]; then
  echo "No files changed."
  exit 0
fi

echo "Changed files found:"
echo "$changed_files"
echo ""

# Find directories containing package.json or other dependency files for vulnerability scanning
# Also include any changed package.json, Dockerfile, or config files directly
dependency_dirs=()
direct_scan_files=()

while IFS= read -r file; do
  if [[ -f "$file" ]]; then
    # Check if it's a dependency or config file that should be scanned directly
    if [[ "$file" =~ \.(package\.json|package-lock\.json|yarn\.lock|Dockerfile|docker-compose\.yml)$ ]]; then
      direct_scan_files+=("$file")
    fi
    
    # Check if the directory contains package.json for dependency scanning
    dir=$(dirname "$file")
    if [[ -f "$dir/package.json" ]] && [[ ! " ${dependency_dirs[@]} " =~ " ${dir} " ]]; then
      dependency_dirs+=("$dir")
    fi
  fi
done <<< "$changed_files"

# Scan dependency directories
if [[ ${#dependency_dirs[@]} -gt 0 ]]; then
  echo "Scanning directories with dependencies:"
  for dir in "${dependency_dirs[@]}"; do
    echo "Scanning directory: $dir"
    trivy -q -c trivy.yaml fs "$dir"
  done
fi

# Scan specific dependency/config files
if [[ ${#direct_scan_files[@]} -gt 0 ]]; then
  echo "Scanning dependency and configuration files:"
  for file in "${direct_scan_files[@]}"; do
    echo "Scanning file: $file"
    trivy -q -c trivy.yaml fs "$file"
  done
fi

# Also scan source files for secrets
echo "Scanning changed files for secrets:"
while IFS= read -r file; do
  if [[ -f "$file" ]]; then
    echo "Scanning $file for secrets..."
    trivy -q --scanners secret fs "$file"
  fi
done <<< "$changed_files"
