pipelines:
  pull-requests:
    '**':
      - step:
          name: <PERSON>vy Vulnerability Scan
          image: aquasec/trivy:latest
          script:
            - |
              echo "=== Starting Trivy Security Scan ==="
              # Install Node.js and npm
              apk add --no-cache nodejs npm
              
              # Install dependencies
              echo "Installing npm dependencies..."
              npm install
              
              # Run the same audit command developers use locally
              echo "Running Trivy scan via npm script..."
              npm run audit:trivy | tee trivy-report.txt
              
              # Display summary of findings
              echo "=== Scan Results Summary ==="
              CRITICAL_COUNT=$(grep CRITICAL trivy-report.txt | wc -l)
              HIGH_COUNT=$(grep HIGH trivy-report.txt | wc -l)
              echo "CRITICAL vulnerabilities: ${CRITICAL_COUNT}"
              echo "HIGH vulnerabilities: ${HIGH_COUNT}"
              echo "=== End of Summary ==="
          artifacts:
            - trivy-report.txt
